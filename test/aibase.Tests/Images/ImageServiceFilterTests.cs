using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Shouldly;
using Xunit;
using aibase.Images.Dto;
using aibase.Images.Services.ImageService;

namespace aibase.Tests.Images
{
    public class ImageServiceFilterTests : aibaseTestBase
    {
        private readonly IImageService _imageService;

        public ImageServiceFilterTests()
        {
            _imageService = Resolve<IImageService>();
        }

        [Fact]
        public async Task GetAllByViewAsync_WithEmptyImageFilterDto_ShouldThrowException()
        {
            // Arrange
            var request = new PagedImageViewRequestDto
            {
                HoleIds = "[1]",
                ImageFilterDto = new List<ImageFilterDto>(), // Empty list
                MaxResultCount = 10,
                SkipCount = 0
            };

            // Act & Assert
            var exception = await Should.ThrowAsync<ArgumentException>(
                async () => await _imageService.GetAllByViewAsync(request));
            
            exception.Message.ShouldContain("ImageFilterDto must be provided and contain at least one filter");
        }

        [Fact]
        public async Task GetAllByViewAsync_WithNullImageFilterDto_ShouldThrowException()
        {
            // Arrange
            var request = new PagedImageViewRequestDto
            {
                HoleIds = "[1]",
                ImageFilterDto = null, // Null
                MaxResultCount = 10,
                SkipCount = 0
            };

            // Act & Assert
            var exception = await Should.ThrowAsync<ArgumentException>(
                async () => await _imageService.GetAllByViewAsync(request));
            
            exception.Message.ShouldContain("ImageFilterDto must be provided and contain at least one filter");
        }

        [Fact]
        public async Task GetAllByViewAsync_WithValidImageFilterDto_ShouldNotThrowException()
        {
            // Arrange
            var request = new PagedImageViewRequestDto
            {
                HoleIds = "[1]",
                ImageFilterDto = new List<ImageFilterDto>
                {
                    new() { ImageTypeId = 1 }
                },
                MaxResultCount = 10,
                SkipCount = 0
            };

            // Act & Assert - Should not throw exception even if no data exists
            var result = await _imageService.GetAllByViewAsync(request);
            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task GetAllByViewFilteredAsync_WithEmptyImageFilterDto_ShouldThrowException()
        {
            // Arrange
            var request = new PagedImageViewRequestDto
            {
                HoleIds = "[1]",
                ImageFilterDto = new List<ImageFilterDto>(), // Empty list
                MaxResultCount = 10,
                SkipCount = 0
            };

            // Act & Assert
            var exception = await Should.ThrowAsync<ArgumentException>(
                async () => await _imageService.GetAllByViewFilteredAsync(request));
            
            exception.Message.ShouldContain("ImageFilterDto must be provided and contain at least one filter");
        }

        [Fact]
        public async Task GetAllByViewFilteredAsync_WithValidImageFilterDto_ShouldNotThrowException()
        {
            // Arrange
            var request = new PagedImageViewRequestDto
            {
                HoleIds = "[1]",
                ImageFilterDto = new List<ImageFilterDto>
                {
                    new() { ImageTypeId = 1 },
                    new() { ImageTypeId = 2, ImageSubtypeId = 1 }
                },
                MaxResultCount = 10,
                SkipCount = 0
            };

            // Act & Assert - Should not throw exception even if no data exists
            var result = await _imageService.GetAllByViewFilteredAsync(request);
            result.ShouldNotBeNull();
        }
    }
}
