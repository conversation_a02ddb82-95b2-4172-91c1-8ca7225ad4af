using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.GeologySuiteFields;

namespace aibase.GeologyUrlValues;

public class GeologyUrlValue : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public int DataEntryId { get; set; }
    
    [Required]
    public int GeologySuiteFieldId { get; set; }
    public virtual GeologySuiteField GeologySuiteField { get; set; }
    
    public string? Url { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}