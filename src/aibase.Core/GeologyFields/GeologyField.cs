using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;
using aibase.GeologyCheckBoxs;
using aibase.GeologyDates;
using aibase.GeologyDescriptions;
using aibase.GeologyLatitudes;
using aibase.GeologyLongitudes;
using aibase.GeologyUrls;
using aibase.Numbers;
using aibase.PickLists;
using aibase.RockGroups;
using aibase.RockSelectNumbers;
using aibase.RockTree;
using aibase.RockTypeNumbers;

namespace aibase.GeologyFields;

public class GeologyField : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public string Code { get; set; } = string.Empty;
    
    [Required]
    public FieldType Type { get; set; }
    
    [Required]
    public string BackgroundColour { get; set; } = string.Empty;
    
    [Required]
    public string TextColour { get; set; } = string.Empty;
    
    
    [Required]
    public bool IsActive { get; set; }
    
    public bool IsMandatory { get; set; } = false;
    
    [Required]
    public int TenantId { get; set; }
    
    public int? NumberId { get; set; }
    public virtual Number Number { get; set; }    
    public int? RockGroupId { get; set; }
    public virtual RockGroup RockGroup { get; set; }    
    public int? PickListId { get; set; }
    public virtual PickList PickList { get; set; }    
    public int? GeologyDescriptionId { get; set; }
    public virtual GeologyDescription? GeologyDescription { get; set; }
    
    public int? RockTypeNumberId { get; set; }
    public virtual RockTypeNumber? RockTypeNumber { get; set; }
    
    public int? RockSelectNumberId { get; set; }
    public virtual RockSelectNumber? RockSelectNumber { get; set; }
    
    public int? GeologyDateId { get; set; }
    public virtual GeologyDate? GeologyDate { get; set; }
    
    public int? RockNodeId { get; set; }
    public virtual RockNode? RockNode { get; set; }
    
    public int? GeologyCheckBoxId { get; set; }
    public virtual GeologyCheckBox? GeologyCheckBox { get; set; }
    
    public int? GeologyLatitudeId { get; set; }
    public virtual GeologyLatitude? GeologyLatitude { get; set; }
    
    public int? GeologyLongitudeId { get; set; }
    public virtual GeologyLongitude? GeologyLongitude { get; set; }
    
    public int? GeologyUrlId { get; set; }
    public virtual GeologyUrl? GeologyUrl { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}

public enum FieldType
{
    Colour = 1,
    Number,
    RockGroup,
    PickList,
    Description,
    RockTypeNumber,
    RockSelectNumber,
    Date,
    RockNode,
    CheckBox,
    Latitude,
    Longitude,
    Url
}