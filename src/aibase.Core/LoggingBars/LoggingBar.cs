using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;

namespace aibase.LoggingBars;

public class LoggingBar : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public int GeologySuiteId { get; set; }
    
    [Required]
    public int DrillHoleId { get; set; }
    
    [Required]
    public int ImageTypeId { get; set; }
    
    public int? ImageSubtypeId { get; set; }
    
    [Required]
    public double DepthFrom { get; set; }
    
    [Required]
    public int StartImageCropId { get; set; }
    
    [Required]
    public double StartX { get; set; }
    
    [Required]
    public double DepthTo { get; set; }
    
    [Required]
    public int EndImageCropId { get; set; }
    
    [Required]
    public double EndX { get; set; }
    
    public List<int> BetweenImageCropIds { get; set; } = [];
    
    public int DataEntryId { get; set; }
    
    public int TenantId { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}