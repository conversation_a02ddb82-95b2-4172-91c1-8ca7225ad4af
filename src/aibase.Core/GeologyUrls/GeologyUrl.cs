using System;
using System.ComponentModel.DataAnnotations;
using Abp.Domain.Entities;
using Abp.Domain.Entities.Auditing;

namespace aibase.GeologyUrls;

public class GeologyUrl : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required] 
    public string Name { get; set; } = string.Empty;
    
    [Required] 
    public bool IsActive { get; set; }
    
    [Required]
    public int TenantId { get; set; }
    
    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}