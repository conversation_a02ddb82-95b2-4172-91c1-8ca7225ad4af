﻿using Abp.Domain.Entities.Auditing;
using Abp.Domain.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using aibase.Polygons;
using aibase.SourceTypeWorkflowEntity;

namespace aibase.StepWorkflowEntity;

public class StepWorkflow : Entity<int>, IHasCreationTime, IHasModificationTime
{
    [Required]
    public string Name { get; set; } = string.Empty;

    [Required]
    public int WorkflowId { get; set; }

    [Required]
    public ProcessType ProcessType { get; set; }

    public AiModelType? ModelId { get; set; }

    public ToolType? ToolType { get; set; }

    public int? PolygonId { get; set; }

    [Required]
    public DataSourceType DataSourceType { get; set; }
        
    [Required]
    public Boolean SegmentFlag { get; set; } = false;

    [Required]
    public string DataValue { get; set; } = string.Empty;

    [Required]
    public OutputType OutputType { get; set; }

    public int? BoundingBoxId { get; set; }
    public virtual Polygon? BoundingBox { get; set; }

    public int? BoundingRowsId { get; set; }
    public virtual Polygon? BoundingRows { get; set; }

    public BoundingRowOption? BoundingRowOption { get; set; } = StepWorkflowEntity.BoundingRowOption.Polygon;
        
    public string? Prompt { get; set; }
        
    public bool IsCropAdditional { get; set; } = false;
    
    public List<int>? ImageTypesAdditional { get; set; } = [];
    
    public List<int>? ImageSubtypesAdditional { get; set; } = [];

    public DateTime CreationTime { get; set; }
    public DateTime? LastModificationTime { get; set; }
}

public enum ToolType
{
    Crop = 1,
    RotateClockwise,
    RotateCounterClockwise,
    Align,
    AssignRow,
    AssignBox
}

public enum DataSourceType
{
    Step = 1,
    Image
}

public enum OutputType
{
    Image = 1,
    Text,
    Coordinate
}

public enum BoundingRowOption{
    Polygon = 1,
    AutoGenerate
}

public enum AiModelType
{
    Ocr = 1,
    Segment = 2,
    DirectOcr = 3,
    GeneratePolygon = 4,
    PredictDrillholeAndDepth = 5,
    DetectFractures = 6,
    NewSegment = 7,
    PolygonCropOcrSegment = 8,
    GeminiAi = 9,
    SegmentationDetail = 10,
    PolygonCrop = 11,
    PolygonCropOcrSegmentDetailSam = 12,
    PolygonCropOcrSegmentDetailCorePieces = 13,
    SegmentAutoCrop = 14,
    ProcessCoreOutline = 15,
    SegmentCorePieces = 16,
}