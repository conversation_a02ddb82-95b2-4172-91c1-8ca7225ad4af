using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using aibase.GeologyLongitudes.Dto;
using aibase.GeologyLongitudes.Services.GeologyLongitudeService;

namespace aibase.GeologyLongitudes;

/// <summary>
/// 
/// </summary>
public class GeologyLongitudeAppService : AsyncCrudAppService<GeologyLongitude, GeologyLongitudeDto, int,
    PagedGeologyLongitudeResultRequestDto,
    CreateGeologyLongitudeDto, UpdateGeologyLongitudeDto>, IGeologyLongitudeAppService
{
    private readonly IGeologyLongitudeService _geologyLongitudeService;

    /// <inheritdoc />
    public GeologyLongitudeAppService(IRepository<GeologyLongitude, int> repository,
        IGeologyLongitudeService geologyLongitudeService) : base(repository)
    {
        _geologyLongitudeService = geologyLongitudeService;
    }
    
    /// <inheritdoc />
    public override async Task<GeologyLongitudeDto> CreateAsync(CreateGeologyLongitudeDto input)
    {
        var geologyLongitude = await _geologyLongitudeService.CreateAsync(input);
        return MapToEntityDto(geologyLongitude);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<GeologyLongitudeDto>> GetAllAsync(PagedGeologyLongitudeResultRequestDto input)
    {
        return await _geologyLongitudeService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyLongitudeDto> GetAsync(EntityDto<int> input)
    {
        return await _geologyLongitudeService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyLongitudeDto> UpdateAsync(UpdateGeologyLongitudeDto input)
    {
        return await _geologyLongitudeService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public override async Task DeleteAsync(EntityDto<int> input)
    {
        await _geologyLongitudeService.DeleteAsync(input);
    }
}