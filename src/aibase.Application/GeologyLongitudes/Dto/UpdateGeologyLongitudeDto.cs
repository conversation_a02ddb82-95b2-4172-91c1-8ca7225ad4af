using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;
using AutoMapper;

namespace aibase.GeologyLongitudes.Dto;

/// <inheritdoc />
[AutoMap(typeof(GeologyLongitude))]
public class UpdateGeologyLongitudeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool? IsActive { get; set; }
}