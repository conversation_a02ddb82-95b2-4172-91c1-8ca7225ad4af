using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyLongitudes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyLongitudes.Services.GeologyLongitudeService;

/// <inheritdoc />
public class GeologyLongitudeService : IGeologyLongitudeService
{
    private readonly IRepository<GeologyLongitude, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public GeologyLongitudeService(IRepository<GeologyLongitude, int> repository, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<GeologyLongitude> CreateAsync(CreateGeologyLongitudeDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyLongitude =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyLongitude != null)
        {
            if (returnExist)
            {
                return existingGeologyLongitude;
            }
            
            throw new UserFriendlyException($"The Geology Longitude with the name {existingGeologyLongitude.Name} already exists.");
        }
        
        var geologyLongitude = new GeologyLongitude()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyLongitude);
        return geologyLongitude;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyLongitudeDto>> GetAllAsync(PagedGeologyLongitudeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var itemsDto = _mapper.Map<List<GeologyLongitudeDto>>(items);

        return new PagedResultDto<GeologyLongitudeDto>(totalCount, itemsDto);
    }

    /// <inheritdoc />
    public async Task<GeologyLongitudeDto> GetAsync(EntityDto<int> input)
    {
        var geologyLongitude = await ValidateGeologyLongitudeEntity(input.Id);
        return _mapper.Map<GeologyLongitudeDto>(geologyLongitude);
    }

    /// <inheritdoc />
    public async Task<GeologyLongitudeDto> UpdateAsync(UpdateGeologyLongitudeDto input)
    {
        var geologyLongitude = await ValidateGeologyLongitudeEntity(input.Id);
        
        geologyLongitude.Name = input.Name ?? geologyLongitude.Name;
        geologyLongitude.IsActive = input.IsActive ?? geologyLongitude.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyLongitude = await ValidateGeologyLongitudeEntity(input.Id);
        await _repository.DeleteAsync(geologyLongitude);
    }

    private async Task<GeologyLongitude> ValidateGeologyLongitudeEntity(int id)
    {
        var item = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (item == null)
        {
            throw new EntityNotFoundException(typeof(GeologyLongitude), id);
        }

        return item;
    }
}