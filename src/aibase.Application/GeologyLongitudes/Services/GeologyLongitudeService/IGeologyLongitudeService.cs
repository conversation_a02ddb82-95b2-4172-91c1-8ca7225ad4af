using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.GeologyLongitudes.Dto;

namespace aibase.GeologyLongitudes.Services.GeologyLongitudeService;

/// <inheritdoc />
public interface IGeologyLongitudeService : ITransientDependency
{
    /// <summary>
    /// Creates a new GeologyLongitude
    /// </summary>
    Task<GeologyLongitude> CreateAsync(CreateGeologyLongitudeDto input, bool returnExist = false);

    /// <summary>
    /// Gets all GeologyLongitudes with paging and filtering
    /// </summary>
    Task<PagedResultDto<GeologyLongitudeDto>> GetAllAsync(PagedGeologyLongitudeResultRequestDto input);

    /// <summary>
    /// Gets a specific GeologyLongitude by id
    /// </summary>
    Task<GeologyLongitudeDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Updates an existing GeologyLongitude
    /// </summary>
    Task<GeologyLongitudeDto> UpdateAsync(UpdateGeologyLongitudeDto input);

    /// <summary>
    /// Deletes a GeologyLongitude
    /// </summary>
    Task DeleteAsync(EntityDto<int> input);
}