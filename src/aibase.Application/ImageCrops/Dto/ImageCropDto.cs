﻿using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.Images.Dto;
using aibase.RockLines.Dto;
using AutoMapper;

namespace aibase.ImageCrops.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(ImageCrop))]
    public class ImageCropDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public int ImageId { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string UrlCroppedImage { get; set; } = string.Empty;
        
        /// <summary>
        /// 
        /// </summary>
        public string MediumSize { get; set; } = string.Empty;
        
        /// <summary>
        /// 
        /// </summary>
        public string? Type { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? Coordinate { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public CoordinateBoundingDto CoordinateBounds { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public double? DepthFrom { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public double? DepthTo { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? SegmentResutl { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<RockLineDto> RockLines { get; set; } = [];
    }
}
