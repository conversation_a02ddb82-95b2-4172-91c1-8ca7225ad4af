using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyCheckBoxs.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyCheckBoxs.Services.GeologyCheckboxService;

/// <inheritdoc />
public class GeologyCheckBoxService : IGeologyCheckBoxService
{
    private readonly IRepository<GeologyCheckBox, int> _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public GeologyCheckBoxService(
        IRepository<GeologyCheckBox, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<GeologyCheckBox> CreateAsync(CreateGeologyCheckBoxDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyCheckbox =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyCheckbox != null)
        {
            if (returnExist)
            {
                return existingGeologyCheckbox;
            }
            
            throw new UserFriendlyException($"The Geology Checkbox with the name {existingGeologyCheckbox.Name} already exists.");
        }
        
        var geologyCheckbox = new GeologyCheckBox()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyCheckbox);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return geologyCheckbox;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyCheckBoxDto>> GetAllAsync(PagedGeologyCheckBoxResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var itemsDto = _mapper.Map<List<GeologyCheckBoxDto>>(items);

        return new PagedResultDto<GeologyCheckBoxDto>(totalCount, itemsDto);
    }

    /// <inheritdoc />
    public async Task<GeologyCheckBoxDto> GetAsync(EntityDto<int> input)
    {
        var geologyCheckbox = await ValidateGeologyCheckboxEntity(input.Id);
        return _mapper.Map<GeologyCheckBoxDto>(geologyCheckbox);
    }

    /// <inheritdoc />
    public async Task<GeologyCheckBoxDto> UpdateAsync(UpdateGeologyCheckBoxDto input)
    {
        var geologyCheckbox = await ValidateGeologyCheckboxEntity(input.Id);
        
        geologyCheckbox.Name = input.Name ?? geologyCheckbox.Name;
        geologyCheckbox.IsActive = input.IsActive ?? geologyCheckbox.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyCheckbox = await ValidateGeologyCheckboxEntity(input.Id);
        await _repository.DeleteAsync(geologyCheckbox);
    }

    private async Task<GeologyCheckBox> ValidateGeologyCheckboxEntity(int id)
    {
        var item = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (item == null)
        {
            throw new EntityNotFoundException(typeof(GeologyCheckBox), id);
        }

        return item;
    }
}