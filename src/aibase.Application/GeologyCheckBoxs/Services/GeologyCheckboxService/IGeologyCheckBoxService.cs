using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.GeologyCheckBoxs.Dto;

namespace aibase.GeologyCheckBoxs.Services.GeologyCheckboxService;

/// <inheritdoc />
public interface IGeologyCheckBoxService : ITransientDependency
{
    /// <summary>
    /// Creates a new GeologyCheckbox
    /// </summary>
    Task<GeologyCheckBox> CreateAsync(CreateGeologyCheckBoxDto input, bool returnExist = false);

    /// <summary>
    /// Gets all GeologyCheckboxs with paging and filtering
    /// </summary>
    Task<PagedResultDto<GeologyCheckBoxDto>> GetAllAsync(PagedGeologyCheckBoxResultRequestDto input);

    /// <summary>
    /// Gets a specific GeologyCheckbox by id
    /// </summary>
    Task<GeologyCheckBoxDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Updates an existing GeologyCheckbox
    /// </summary>
    Task<GeologyCheckBoxDto> UpdateAsync(UpdateGeologyCheckBoxDto input);

    /// <summary>
    /// Deletes a GeologyCheckbox
    /// </summary>
    Task DeleteAsync(EntityDto<int> input);
}