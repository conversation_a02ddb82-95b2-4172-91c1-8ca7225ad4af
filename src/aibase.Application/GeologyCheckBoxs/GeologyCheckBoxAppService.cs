using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using aibase.GeologyCheckBoxs.Dto;
using aibase.GeologyCheckBoxs.Services.GeologyCheckboxService;

namespace aibase.GeologyCheckBoxs;

/// <summary>
/// 
/// </summary>
public class GeologyCheckBoxAppService : AsyncCrudAppService<GeologyCheckBox, GeologyCheckBoxDto, int, PagedGeologyCheckBoxResultRequestDto,
    CreateGeologyCheckBoxDto, UpdateGeologyCheckBoxDto>, IGeologyCheckBoxAppService
{
    private readonly IGeologyCheckBoxService _geologyCheckBoxService;

    /// <inheritdoc />
    public GeologyCheckBoxAppService(IRepository<GeologyCheckBox, int> repository, IGeologyCheckBoxService geologyCheckBoxService) 
        : base(repository)
    {
        _geologyCheckBoxService = geologyCheckBoxService;
    }

    /// <inheritdoc />
    public override async Task<GeologyCheckBoxDto> CreateAsync(CreateGeologyCheckBoxDto input)
    {
        var geologyCheckbox = await _geologyCheckBoxService.CreateAsync(input);
        return MapToEntityDto(geologyCheckbox);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<GeologyCheckBoxDto>> GetAllAsync(PagedGeologyCheckBoxResultRequestDto input)
    {
        return await _geologyCheckBoxService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyCheckBoxDto> GetAsync(EntityDto<int> input)
    {
        return await _geologyCheckBoxService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyCheckBoxDto> UpdateAsync(UpdateGeologyCheckBoxDto input)
    {
        return await _geologyCheckBoxService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public override async Task DeleteAsync(EntityDto<int> input)
    {
        await _geologyCheckBoxService.DeleteAsync(input);
    }
}