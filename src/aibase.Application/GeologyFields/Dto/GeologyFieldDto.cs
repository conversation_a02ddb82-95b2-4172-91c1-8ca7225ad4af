using System;
using Abp.Application.Services.Dto;
using Abp.AutoMapper;
using aibase.Colours.Dto;
using aibase.GeologyCheckBoxs.Dto;
using aibase.GeologyDates.Dto;
using aibase.GeologyDescriptions.Dto;
using aibase.GeologyLatitudes.Dto;
using aibase.GeologyLongitudes.Dto;
using aibase.GeologyUrls.Dto;
using aibase.Numbers.Dto;
using aibase.PickLists.Dto;
using aibase.RockGroups.Dto;
using aibase.RockSelectNumbers.Dto;
using aibase.RockTree.Dto;
using aibase.RockTypeNumbers.Dto;

namespace aibase.GeologyFields.Dto;

/// <inheritdoc />
[AutoMap(typeof(GeologyField))]
public class GeologyFieldDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public FieldType Type { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public string BackgroundColour { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public string TextColour { get; set; } = string.Empty;
    
    /// <summary>
    /// 
    /// </summary>
    public int ReferenceId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public bool IsActive { get; set; }
    
    /// <summary>
    /// Whether this field is mandatory for data entry
    /// </summary>
    public bool IsMandatory { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public ColourDto? Colour { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? NumberId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public NumberDto? Number { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockGroupId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public RockGroupDto? RockGroup { get; set; }

    /// <summary>
    /// 
    /// </summary>
    public int? PickListId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public PickListDto? PickList { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyDescriptionId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyDescriptionDto? GeologyDescription { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyDateId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyDateDto? GeologyDate { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockTypeNumberId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public RockTypeNumberDto? RockTypeNumber { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockSelectNumberId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public RockSelectNumberDto? RockSelectNumber { get; set; }
    
    
    /// <summary>
    /// 
    /// </summary>
    public int? RockNodeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public RockNodeDto? RockNode { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public TreeNodeDto TreeNode { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyCheckBoxId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyCheckBoxDto GeologyCheckBox { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyLatitudeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyLatitudeDto GeologyLatitude { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyLongitudeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyLongitudeDto GeologyLongitude { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? GeologyUrlId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public GeologyUrlDto GeologyUrl { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public DateTime CreationTime { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public DateTime? LastModificationTime { get; set; }
}