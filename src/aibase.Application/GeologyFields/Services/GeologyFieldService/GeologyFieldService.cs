using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyFields.Dto;
using aibase.GeologySuiteFields;
using aibase.RockSelectNumbers;
using aibase.RockTypeNumbers;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyFields.Services.GeologyFieldService;

/// <inheritdoc />
public class GeologyFieldService : IGeologyFieldService
{
    private readonly IRepository<GeologyField, int> _repository;
    private readonly IRepository<GeologySuiteField, int> _geologySuiteFieldRepository;
    private readonly IRepository<RockTypeNumber> _rockTypeNumberRepository;
    private readonly IRepository<RockSelectNumber> _rockSelectNumberRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;
    private readonly ILogger<GeologyFieldService> _logger;

    /// <summary>
    /// 
    /// </summary>
    public GeologyFieldService(
        IRepository<GeologyField, int> repository,
        IRepository<GeologySuiteField, int> geologyFieldRepository,
        IRepository<RockTypeNumber> rockTypeNumberRepository,
        IRepository<RockSelectNumber> rockSelectNumberRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper,
        ILogger<GeologyFieldService> logger
        )
    {
        _repository = repository;
        _geologySuiteFieldRepository = geologyFieldRepository;
        _rockTypeNumberRepository = rockTypeNumberRepository;
        _rockSelectNumberRepository = rockSelectNumberRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
        _logger = logger;
    }

    /// <inheritdoc />
    public async Task<GeologyField> CreateAsync(CreateGeologyFieldDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();
        
        var existingGeologyField =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyField != null)
        {
            if (returnExist)
            {
                return existingGeologyField;
            }
            
            throw new UserFriendlyException($"The Geology Field with the name {existingGeologyField.Name} already exists.");
        }
        
        var getRef = new GetReferenceIdDto
        {
            Type = input.Type,
            NumberId = input.NumberId,
            RockTypeId = input.RockTypeId,
            RockGroupId = input.RockGroupId
        };
        var referenceId = input.ReferenceId ?? await GetReferenceIdAsync(getRef);

        var geologyField = new GeologyField()
        {
            Name = input.Name,
            Code = input.Code,
            Type = input.Type,
            BackgroundColour = input.BackgroundColour,
            TextColour = input.TextColour,
            IsActive = input.IsActive,
            IsMandatory = input.IsMandatory,
            TenantId = _abpSession.GetTenantId(),
        };

        switch (input.Type)
        {
            case FieldType.Colour:
                break;
            case FieldType.Number:
                geologyField.NumberId = referenceId;
                break;
            case FieldType.RockGroup:
                geologyField.RockGroupId = referenceId;
                break;
            case FieldType.PickList:
                geologyField.PickListId = referenceId;
                break;
            case FieldType.Description:
                geologyField.GeologyDescriptionId = referenceId;
                break;
            case FieldType.RockTypeNumber:
                geologyField.RockTypeNumberId = referenceId;
                break;
            case FieldType.RockSelectNumber:
                geologyField.RockSelectNumberId = referenceId;
                break;
            case FieldType.Date:
                geologyField.GeologyDateId = referenceId;
                break;
            case FieldType.RockNode:
                geologyField.RockNodeId = input.RockNodeId;
                break;
            case FieldType.CheckBox:
                geologyField.GeologyCheckBoxId = referenceId;
                break;
            case FieldType.Latitude:
                geologyField.GeologyLatitudeId = referenceId;
                break;
            case FieldType.Longitude:
                geologyField.GeologyLongitudeId = referenceId;
                break;
            case FieldType.Url:
                geologyField.GeologyUrlId = referenceId;
                break;
            default:
                throw new UserFriendlyException("Invalid field type.");
        }

        await _repository.InsertAsync(geologyField);
        return geologyField;
    }

    /// <inheritdoc />
    public async Task<GeologyFieldDto> UpdateAsync(UpdateGeologyFieldDto input)
    {
        var geologyField = await ValidateGeologyFieldEntity(input.Id);

        if (input.IsActive == false)
        {
            var geologyFieldUsed = await _geologySuiteFieldRepository.FirstOrDefaultAsync(x => x.GeologyFieldId == input.Id);
            if (geologyFieldUsed != null)
            {
                throw new UserFriendlyException("Cannot deactivate this record because it is already in use.");
            }
        }
        
        var getRef = new GetReferenceIdDto
        {
            Type = input.Type ?? geologyField.Type,
            NumberId = input.NumberId,
            RockTypeId = input.RockTypeId,
            RockGroupId = input.RockGroupId
        };
        var referenceId = input.ReferenceId ?? await GetReferenceIdAsync(getRef); 

        geologyField.Name = input.Name ?? geologyField.Name;
        geologyField.Code = input.Code ?? geologyField.Code;
        geologyField.Type = input.Type ?? geologyField.Type;
        geologyField.BackgroundColour = input.BackgroundColour ?? geologyField.BackgroundColour;
        geologyField.TextColour = input.TextColour ?? geologyField.TextColour;
        geologyField.IsActive = input.IsActive ?? geologyField.IsActive;
        geologyField.IsMandatory = input.IsMandatory ?? geologyField.IsMandatory;
        
        switch (input.Type)
        {
            case FieldType.Colour:
                break;
            case FieldType.Number:
                geologyField.NumberId = referenceId;
                break;
            case FieldType.RockGroup:
                geologyField.RockGroupId = referenceId;
                break;
            case FieldType.PickList:
                geologyField.PickListId = referenceId;
                break;
            case FieldType.Description:
                geologyField.GeologyDescriptionId = referenceId;
                break;
            case FieldType.RockTypeNumber:
                geologyField.RockTypeNumberId = referenceId;
                break;
            case FieldType.RockSelectNumber:
                geologyField.RockSelectNumberId = referenceId;
                break;
            case FieldType.Date:
                geologyField.GeologyDateId = referenceId;
                break;
            case FieldType.RockNode:
                geologyField.RockNodeId = input.RockNodeId;
                break;
            case FieldType.CheckBox:
                geologyField.GeologyCheckBoxId = referenceId;
                break;
            case FieldType.Latitude:
                geologyField.GeologyLatitudeId = referenceId;
                break;
            case FieldType.Longitude:
                geologyField.GeologyLongitudeId = referenceId;
                break;
            case FieldType.Url:
                geologyField.GeologyUrlId = referenceId;
                break;
            default:
                throw new UserFriendlyException("Invalid field type.");
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyFieldDto>> GetAllAsync(PagedGeologyFieldResultRequestDto input)
{
    IQueryable<GeologyField> query = QueryGeologyField();

    if (input.IsActive.HasValue)
    {
        query = query.Where(x => x.IsActive == input.IsActive);
    }
    
    var keyword = input.Keyword?.Trim().ToLower();
    var hasKeyword = !string.IsNullOrWhiteSpace(keyword);

    if (hasKeyword)
    {
        var keywordIsEnumName = Enum.TryParse(input.Keyword?.Trim(), true, out FieldType typeByName);

        query = query.Where(x =>
            x.Name.ToLower().Contains(keyword) ||

            (keywordIsEnumName && x.Type == typeByName) ||

            (x.Type == FieldType.Number && x.NumberId.HasValue && x.Number.Name.ToLower().Contains(keyword)) ||

            (x.Type == FieldType.RockGroup && x.RockGroupId.HasValue && x.RockGroup.Name.ToLower().Contains(keyword)) ||

            (x.Type == FieldType.PickList && x.PickListId.HasValue && x.PickList.Name.ToLower().Contains(keyword)) ||

            (x.Type == FieldType.Description && x.GeologyDescriptionId.HasValue && x.GeologyDescription != null && x.GeologyDescription.Name.ToLower().Contains(keyword)) ||

            (x.Type == FieldType.RockTypeNumber && x.RockTypeNumberId.HasValue && x.RockTypeNumber != null &&
                ((x.RockTypeNumber.RockType.Name.ToLower().Contains(keyword)) ||
                 (x.RockTypeNumber.Number.Name.ToLower().Contains(keyword)) ||
                 x.RockTypeNumber.Name.ToLower().Contains(keyword))
            ) ||

            (x.Type == FieldType.RockSelectNumber && x.RockSelectNumberId.HasValue && x.RockSelectNumber != null &&
                ((x.RockSelectNumber.RockGroup.Name.ToLower().Contains(keyword)) ||
                 (x.RockSelectNumber.Number.Name.ToLower().Contains(keyword)) ||
                 x.RockSelectNumber.Name.ToLower().Contains(keyword))
            ) ||

            (x.Type == FieldType.Date && x.GeologyDateId.HasValue && x.GeologyDate != null && x.GeologyDate.Name.ToLower().Contains(keyword)) || 
            (x.Type == FieldType.CheckBox && x.GeologyCheckBoxId.HasValue && x.GeologyCheckBox != null && x.GeologyCheckBox.Name.ToLower().Contains(keyword)) || 
            (x.Type == FieldType.Latitude && x.GeologyLatitudeId.HasValue && x.GeologyLatitude != null && x.GeologyLatitude.Name.ToLower().Contains(keyword)) || 
            (x.Type == FieldType.Longitude && x.GeologyLongitudeId.HasValue && x.GeologyLongitude != null && x.GeologyLongitude.Name.ToLower().Contains(keyword)) || 
            (x.Type == FieldType.Url && x.GeologyUrlId.HasValue && x.GeologyUrl != null && x.GeologyUrl.Name.ToLower().Contains(keyword))
        );
    }

    var totalCount = await query.CountAsync();

    var geologyFields = await query
        .Skip(input.SkipCount)
        .Take(input.MaxResultCount)
        .ToListAsync();

    var geologyFieldDto = _mapper.Map<List<GeologyFieldDto>>(geologyFields);

    foreach (var geologyField in geologyFieldDto)
    {
        try
        {
            switch (geologyField.Type)
            {
                case FieldType.Colour:
                    break;
                case FieldType.Number:
                    if (geologyField.NumberId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.NumberId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.NumberId;
                    break;
                case FieldType.RockGroup:
                    if (geologyField.RockGroupId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.RockGroupId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.RockGroupId;
                    break;
                case FieldType.PickList:
                    if (geologyField.PickListId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.PickListId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.PickListId;
                    break;
                case FieldType.Description:
                    if (geologyField.GeologyDescriptionId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyDescriptionId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyDescriptionId;
                    break;
                case FieldType.RockTypeNumber:
                    if (geologyField.RockTypeNumberId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.RockTypeNumberId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.RockTypeNumberId;
                    break;
                case FieldType.RockSelectNumber:
                    if (geologyField.RockSelectNumberId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.RockSelectNumberId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.RockSelectNumberId;
                    break;
                case FieldType.Date:
                    if (geologyField.GeologyDateId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyDateId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyDateId;
                    break;
                case FieldType.CheckBox:
                    if (geologyField.GeologyCheckBoxId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyCheckBoxId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyCheckBoxId;
                    break;
                case FieldType.Latitude:
                    if (geologyField.GeologyLatitudeId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyLatitudeId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyLatitudeId;
                    break;
                case FieldType.Longitude:
                    if (geologyField.GeologyLongitudeId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyLongitudeId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyLongitudeId;
                    break;
                case FieldType.Url:
                    if (geologyField.GeologyUrlId == null)
                    {
                        _logger.LogWarning("Required ID {PropertyName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.GeologyUrlId), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = (int)geologyField.GeologyUrlId;
                    break;
                case FieldType.RockNode:
                    if (geologyField.RockNode == null) // Assuming RockNode.Id could also be an issue, though less likely if RockNode itself is the primary nullable object.
                    {
                        _logger.LogWarning("Required ID {PropertyName} or its parent {ParentName} is null for geologyField.Id {GeologyFieldId}. Skipping processing for this case.", nameof(geologyField.RockNode.Id), nameof(geologyField.RockNode), geologyField.Id);
                        continue;
                    }
                    geologyField.ReferenceId = geologyField.RockNode.Id;
                    break;
                default:
                    throw new UserFriendlyException($"Invalid field type encountered while mapping DTO: {geologyField.Type}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing geology field {GeologyFieldId}", geologyField.Id);
            // Optionally rethrow, or continue to process other items if that's the desired behavior.
            // For now, just logging as per minimum requirement.
        }
    }

    return new PagedResultDto<GeologyFieldDto>(totalCount, geologyFieldDto);
}

    
    /// <inheritdoc />
    public IOrderedQueryable<GeologyField> QueryGeologyField()
    {
        return _repository.GetAllIncluding(
                x => x.Number,
                x => x.RockGroup,
                x => x.PickList,
                x => x.GeologyDescription,
                x => x.GeologyDate,
                x => x.RockNode,
                x => x.GeologyCheckBox,
                x => x.GeologyLatitude,
                x => x.GeologyLongitude,
                x => x.GeologyUrl)
            .AsNoTracking()
            .Include(x => x.RockTypeNumber)
            .ThenInclude(x => x.RockType)
            .Include(x => x.RockTypeNumber)
            .ThenInclude(x => x.Number)
            .Include(x => x.RockSelectNumber)
            .ThenInclude(x => x.RockGroup)
            .Include(x => x.RockSelectNumber)
            .ThenInclude(x => x.Number)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .OrderBy(r => r.Name);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyField = await ValidateGeologyFieldEntity(input.Id);

        // Check if the GeologyField is referenced in GeologySuiteFields
        var hasReferences = await _geologySuiteFieldRepository.GetAll()
            .AnyAsync(x => x.GeologyFieldId == input.Id);

        if (hasReferences)
        {
            throw new UserFriendlyException($"Cannot delete the Geology Field '{geologyField.Name}' because it is being used in one or more Geology Suite Fields.");
        }

        await _repository.DeleteAsync(geologyField);
    }

    /// <inheritdoc />
    public async Task<GeologyFieldDto> GetAsync(EntityDto<int> input)
    {
        var geologyField = await ValidateGeologyFieldEntity(input.Id);
        return _mapper.Map<GeologyFieldDto>(geologyField);
    }

    private async Task<GeologyField> ValidateGeologyFieldEntity(int id)
    {
        var geologyField = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (geologyField == null)
        {
            throw new EntityNotFoundException(typeof(GeologyField), id);
        }

        return geologyField;
    }

    private async Task<int?> GetReferenceIdAsync(GetReferenceIdDto input)
    {
        return input.Type switch
        {
            FieldType.RockTypeNumber => await GetRockTypeNumberReferenceIdAsync(input),
            FieldType.RockSelectNumber => await GetRockSelectNumberReferenceIdAsync(input),
            _ => null
        };
    }

    private async Task<int?> GetRockTypeNumberReferenceIdAsync(GetReferenceIdDto input)
    {
        if (input.NumberId == null || input.RockTypeId == null) return null;
        
        var rockTypeNumber = await _rockTypeNumberRepository.FirstOrDefaultAsync(x =>
            x.TenantId == _abpSession.GetTenantId() &&
            x.RockTypeId == input.RockTypeId &&
            x.NumberId == input.NumberId);

        if (rockTypeNumber != null) return rockTypeNumber.Id;

        rockTypeNumber = new RockTypeNumber
        {
            Name = $"{_abpSession.GetTenantId()}-{input.RockTypeId}-{input.NumberId}",
            RockTypeId = (int)input.RockTypeId,
            NumberId = (int)input.NumberId,
            IsActive = true,
            TenantId = _abpSession.GetTenantId(),
        };

        await _rockTypeNumberRepository.InsertAsync(rockTypeNumber);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return rockTypeNumber.Id;
    }

    private async Task<int?> GetRockSelectNumberReferenceIdAsync(GetReferenceIdDto input)
    {
        if (input.NumberId == null || input.RockGroupId == null) return null;

        var rockSelectNumber = await _rockSelectNumberRepository.FirstOrDefaultAsync(x =>
            x.TenantId == _abpSession.GetTenantId() &&
            x.RockGroupId == input.RockGroupId &&
            x.NumberId == input.NumberId);

        if (rockSelectNumber != null) return rockSelectNumber.Id;

        rockSelectNumber = new RockSelectNumber
        {
            Name = $"{_abpSession.GetTenantId()}-{input.RockTypeId}-{input.NumberId}",
            RockGroupId = (int)input.RockGroupId,
            NumberId = (int)input.NumberId,
            IsActive = true,
            TenantId = _abpSession.GetTenantId(),
        };

        await _rockSelectNumberRepository.InsertAsync(rockSelectNumber);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return rockSelectNumber.Id;
    }


}