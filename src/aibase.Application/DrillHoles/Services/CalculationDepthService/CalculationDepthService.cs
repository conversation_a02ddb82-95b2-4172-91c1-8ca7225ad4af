using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.UI;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.DrillHoles.Services.CalculationDepthService.Handler;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Models.Dto;
using aibase.RockLines;
using aibase.RockLines.Dto;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.DrillHoles.Services.CalculationDepthService;

/// <inheritdoc />
public class CalculationDepthService : ICalculationDepthService
{
    private readonly IRepository<Image, int> _imageRepository;

    /// <summary>
    /// 
    /// </summary>
    public CalculationDepthService(IRepository<Image, int> imageRepository)
    {
        _imageRepository = imageRepository;
    }

    /// <inheritdoc />
    public async Task<double?> CalculateDepthAsync(CalculateDepthBySelectPointDto input)
    {
        // Optimize DB query - only select the properties we need
        var rockLines = await _imageRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .SelectMany(x => x.CroppedImages)
            .SelectMany(x => x.RockLines)
            .Where(rockLine => rockLine.Type == RockLineType.Recovery && rockLine.ImageCropId == input.ImageCropId)
            .Select(rockLine => new RockLineDto
            {
                Id = rockLine.Id,
                Type = rockLine.Type,
                DepthFrom = rockLine.DepthFrom,
                DepthTo = rockLine.DepthTo,
                StartX = rockLine.StartX,
                EndX = rockLine.EndX,
                RowIndex = rockLine.RowIndex,
                ImageCropId = rockLine.ImageCropId
            })
            .ToListAsync();

        var calculateDepthInListImageDto = new CalculateDepthInListImageByRockLineDto
        {
            RockLines = rockLines,
            ImageCropId = input.ImageCropId,
            X = input.X,
        };
        return CalculateDepthInListImageByRockLine(calculateDepthInListImageDto);
    }

    private static double? CalculateDepthInListImageByRockLine(CalculateDepthInListImageByRockLineDto input)
    {
        var rockLine = input.RockLines.FirstOrDefault(x => x.StartX <= input.X && input.X <= x.EndX);
        if (rockLine == null)
        {
            return 0;
        }

        var t = (input.X - rockLine.StartX) / (rockLine.EndX - rockLine.StartX);
        
        return rockLine.DepthFrom + t * (rockLine.DepthTo - rockLine.DepthFrom);
    }

    /// <inheritdoc />
    public double? CalculateDepthInListImage(CalculateDepthInListImageDto input)
    {
        var coreTray = input.CoreTray;
        var lengthCoreTray = 0.0;

        var images = input.Images;
        
        // Find the index of the image containing the specified crop ID
        var imageIndexSelected = images.FindIndex(x => x.CroppedImages.Any(img => img.Id == input.ImageCropId));
        if (imageIndexSelected == -1)
        {
            return null;
        }

        var previousIndex = images
            .Take(imageIndexSelected)
            .LastOrDefault(x => !string.IsNullOrEmpty(x.OcrResult) && x.OcrResult != "[]")
            ?.GetIndex(images) ?? 0;

        var nextIndex = images
            .Skip(imageIndexSelected + 1)
            .FirstOrDefault(x => !string.IsNullOrEmpty(x.OcrResult) && x.OcrResult != "[]")
            ?.GetIndex(images) ?? (images.Count - 1);

        var imagesFilter = images.GetRange(previousIndex, nextIndex - previousIndex + 1);

        // Calculate min/max depth more efficiently
        var depthFromMin = imagesFilter.Min(x => x.DepthFrom);
        var depthToMax = imagesFilter.Max(x => x.DepthTo);

        var boundingSegmentOcrs = new List<BoundingSegmentOcrDto>();

        foreach (var image in imagesFilter)
        {
            var cropRowResults = new List<CropRowResultDto>();
            var imageCrops = image.CroppedImages.ToList();

            // Parse JSON data once outside of loops
            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultV2Dto>>(image.OcrResult ?? "[]") ?? [];
            var segmentResult = JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]") ??
                                [];

            // Pre-filter segment cores (avoiding repeated filtering in the loop)
            var segmentCoreRaws = segmentResult
                .Where(x => x.Class == "core")
                .Select(x => new SegmentResultDto
                {
                    Class = x.Class,
                    rowIndex = x.rowIndex,
                    BoundingSegment = x.BoundingSegment
                }).ToList();

            var segmentCores = ProcessOcrItems(ocrResult, segmentCoreRaws);

            // Create start and end blocks in bulk rather than one by one
            var newOcrBlocks = new List<OcrResultV2Dto>();
            foreach (var segment in segmentCores)
            {
                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X,
                    originalX = segment.BoundingSegment.X,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });

                newOcrBlocks.Add(new OcrResultV2Dto
                {
                    type = "node",
                    x = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    originalX = segment.BoundingSegment.X + segment.BoundingSegment.Width,
                    y = segment.BoundingSegment.Y,
                    width = 1,
                    height = 1,
                    rowIndex = segment.rowIndex,
                    text = $"{image.DepthFrom}",
                });
            }

            ocrResult.AddRange(newOcrBlocks);

            // Pre-filter and sort OCRs once
            var ocrs = ocrResult
                .Where(x => double.TryParse(x.text, out var value) && value >= depthFromMin && value <= depthToMax)
                .OrderBy(x => x.rowIndex)
                .ThenBy(x => x.x)
                .ToList();

            var ocrWithoutWooden = new List<OcrResultV2Dto>(ocrs.Count);
            if (ocrs.Count > 0 && ocrs[0].type == "node" && boundingSegmentOcrs.Count == 0)
            {
                ocrWithoutWooden.Add(ocrs[0]);
            }

            // Process nodes in one pass
            for (var i = 1; i < ocrs.Count; i++)
            {
                if (ocrs[i].type != "node") continue;

                var prevWooden = ocrs[i - 1];
                var hasNext = i + 1 < ocrs.Count;
                var nextWooden = hasNext ? ocrs[i + 1] : null;

                var isPrevWooden = prevWooden.type.Equals("wooden", StringComparison.OrdinalIgnoreCase);
                var isNextWooden =
                    hasNext && nextWooden?.type.Equals("wooden", StringComparison.OrdinalIgnoreCase) == true;

                if (!isPrevWooden && !isNextWooden) continue;

                if (isPrevWooden)
                {
                    ocrs[i].text = prevWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
                else if (isNextWooden)
                {
                    ocrs[i].text = nextWooden.text;
                    ocrWithoutWooden.Add(ocrs[i]);
                }
            }

            // Dictionary to cache OCR results by row index for faster lookup
            var ocrResultsByRowIndex = ocrWithoutWooden
                .GroupBy(x => x.rowIndex)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Process image crops and build crop row results
            for (var i = 0; i < imageCrops.Count; i++)
            {
                var coordinateImageCrop =
                    JsonConvert.DeserializeObject<CoordinateBoundingDto>(imageCrops[i].Coordinate ?? "")
                    ?? new CoordinateBoundingDto();

                // Get OCR results for current row index efficiently
                ocrResultsByRowIndex.TryGetValue(i, out var ocrResults);
                ocrResults ??= [];

                if (imageCrops[i].Id == input.ImageCropId)
                {
                    lengthCoreTray = coordinateImageCrop.Width;
                    var selectedX = input.X + coordinateImageCrop.X;
                    var selectedY = coordinateImageCrop.Y + coordinateImageCrop.Height / 2;

                    var rowIndex = CalculationDepthHandler.IsPointInAnyPolygon(segmentCores, selectedX, selectedY);

                    if (rowIndex == null)
                    {
                        return null;
                    }

                    var createOcr = new OcrResultV2Dto
                    {
                        id = Guid.NewGuid().ToString(),
                        type = "selected",
                        x = selectedX,
                        originalX = selectedX,
                        y = selectedY,
                        width = 100,
                        height = 100,
                        text = $"{image.DepthFrom}",
                        probability = 1,
                        rowIndex = rowIndex,
                        ImageCropId = input.ImageCropId,
                    };
                    ocrResults.Add(createOcr);
                }

                // Pre-filter segment cores by row index
                var rowSegments = segmentCores
                    .Where(x => x.rowIndex == i)
                    .Select(x => x.BoundingSegment)
                    .ToList();

                var cropRowResult = new CropRowResultDto
                {
                    ImageCropId = imageCrops[i].Id,
                    X = coordinateImageCrop.X,
                    Y = coordinateImageCrop.Y,
                    Width = coordinateImageCrop.Width,
                    Height = coordinateImageCrop.Height,
                    RowIndex = i,
                    OcrResults = ocrResults.OrderBy(x => x.x).ToList(),
                    BoundingSegment = rowSegments
                };
                cropRowResults.Add(cropRowResult);
            }

            // Process all crop rows at once to build boundingSegmentOcr
            var boundingSegmentOcr = new List<BoundingSegmentOcrDto>();
            foreach (var crop in cropRowResults)
            {
                // Create a spatial index for OCR results to speed up matching
                var ocrByXCoord = crop.OcrResults.ToList();

                foreach (var segment in crop.BoundingSegment)
                {
                    // More efficient matching using a single pass filter
                    var matched = ocrByXCoord
                        .Where(ocr => ocr.x >= segment.X && ocr.x <= (segment.X + segment.Width))
                        .ToList();

                    boundingSegmentOcr.Add(new BoundingSegmentOcrDto
                    {
                        X = segment.X,
                        Y = segment.Y,
                        Width = segment.Width,
                        Height = segment.Height,
                        RowIndex = crop.RowIndex,
                        OcrResults = matched
                    });
                }
            }

            // Sort once before adding to the main collection
            boundingSegmentOcrs.AddRange(boundingSegmentOcr.OrderBy(x => x.RowIndex).ThenBy(x => x.X));
        }

        // Process filterBoundingSegmentOcrs in one pass
        var filterBoundingSegmentOcrs = boundingSegmentOcrs
            .Select(x => new BoundingSegmentOcrDto
            {
                X = 0, // x.X - x.X is always 0
                Y = x.Y,
                Width = x.Width,
                Height = x.Height,
                RowIndex = x.RowIndex,
                OcrResults = x.OcrResults.Select(o => new OcrResultV2Dto
                {
                    id = o.id,
                    type = o.type,
                    x = o.x - x.X,
                    originalX = o.originalX,
                    y = o.y,
                    width = o.width,
                    height = o.height,
                    text = o.text,
                    rowIndex = o.rowIndex
                }).ToList()
            })
            .ToList();

        var alignBoundingSegmentOcrs = CalculationDepthHandler.AlignBoundingSegmentOcrs(filterBoundingSegmentOcrs);

        // Combine these operations to reduce iterations
        var horizontalOcrBlocks = alignBoundingSegmentOcrs
            .SelectMany(row => row.OcrResults)
            .Where(item => double.TryParse(item.text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockDto
            {
                X = item.x,
                OriginalX = item.originalX,
                Y = item.y,
                Width = item.width,
                Height = item.height,
                Value = double.Parse(item.text, CultureInfo.InvariantCulture),
                Type = item.type
            })
            .ToList();

        var selectedIndex = horizontalOcrBlocks.FindIndex(b => b.Type == "selected");

        if (selectedIndex < 1) return null;

        var before = horizontalOcrBlocks[selectedIndex - 1];
        var selected = horizontalOcrBlocks[selectedIndex];

        var xNext = lengthCoreTray + before.X;
        var valueNext = coreTray + before.Value;
        var rate = (selected.X - before.X) / lengthCoreTray;
        if (rate > 1)
        {
            xNext = before.X + lengthCoreTray * Math.Ceiling(rate);
            valueNext = before.Value + coreTray * Math.Ceiling(rate);
        }

        if (horizontalOcrBlocks.Count >= 3)
        {
            var x = (selected.X - before.X) / (xNext - before.X);
            return (valueNext - before.Value) * x + before.Value;
        }

        return null;
    }

    private static List<SegmentResultDto> ProcessOcrItems(List<OcrResultV2Dto> ocrItems, List<SegmentResultDto> rows)
    {
        var outputRows = new List<SegmentResultDto>();

        foreach (var inputRow in rows)
        {
            var rowIndex = inputRow.rowIndex;
            var bounding = inputRow.BoundingSegment;
            var startX = bounding.X;
            var endX = bounding.X + bounding.Width;

            var relevantOcrItems = ocrItems
                .Where(o => o.rowIndex == rowIndex && o.x >= startX && o.x <= endX)
                .ToList();

            var splitPoints = relevantOcrItems
                .Select(o => o.x)
                .Distinct()
                .OrderBy(x => x)
                .ToList();

            var points = new List<double> { startX };
            points.AddRange(splitPoints);
            if (!points.Contains(endX))
            {
                points.Add(endX);
            }

            for (var i = 0; i < points.Count - 1; i++)
            {
                var segStart = points[i];
                var segEnd = points[i + 1];
                if (segEnd > segStart)
                {
                    var newBounding = new CoordinateV2Dto
                    {
                        X = segStart,
                        Y = bounding.Y,
                        Width = segEnd - segStart,
                        Height = bounding.Height
                    };
                    var outputRow = new SegmentResultDto
                    {
                        Class = inputRow.Class,
                        rowIndex = rowIndex,
                        BoundingSegment = newBounding
                    };
                    outputRows.Add(outputRow);
                }
            }
        }

        return outputRows;
    }
}