using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using aibase.DrillHoles.Dto.CalculationRecovery;
using aibase.DrillHoles.Services.RecoveryService.Handler;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.RecoveryResults;
using aibase.RockLines;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.DrillHoles.Services.RecoveryService;

/// <inheritdoc />
public class RecoveryService : IRecoveryService
{
    private readonly IRepository<DrillHole, int> _repository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<RecoveryResult, int> _recoveryResultRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="imageRepository"></param>
    /// <param name="recoveryResultRepository"></param>
    /// <param name="mapper"></param>
    /// <param name="unitOfWorkManager"></param>
    public RecoveryService(
        IRepository<DrillHole, int> repository,
        IRepository<Image, int> imageRepository,
        IRepository<RecoveryResult, int> recoveryResultRepository,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager
    )
    {
        _repository = repository;
        _imageRepository = imageRepository;
        _recoveryResultRepository = recoveryResultRepository;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RecoveryResultDto>> GetRecoveryByDrillHoleAsync(
        PagedRecoveryHoleResultRequestDto input)
    {
        var query = _recoveryResultRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.DrillHoleId == input.DrillHoleId);

        var totalCount = await query.CountAsync();

        var recoveries = await query
            // .Skip(input.SkipCount)
            // .Take(input.MaxResultCount)
            .ToListAsync();

        var recoveriesDto = _mapper.Map<List<RecoveryResultDto>>(recoveries);

        return new PagedResultDto<RecoveryResultDto>(totalCount, recoveriesDto);
    }

    /// <inheritdoc />
    public async Task<List<RecoveryResultDto>> CalculateRecoveryAsync(CalculateRecoveryDto input)
    {
        var drillHole = await _repository.GetAllIncluding(x => x.Project)
                            .FirstOrDefaultAsync(x => x.Id == input.DrillHoleId)
                        ?? throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);

        await _recoveryResultRepository.DeleteAsync(c => c.DrillHoleId == input.DrillHoleId);

        var coreTray = drillHole.Project.CoreTrayLength;

        var imageAll = await _imageRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.ImageType)
            .Include(x => x.ImageSubtype)
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .Where(x => x.DrillHoleId == input.DrillHoleId &&
                        x.ImageType != null &&
                        x.ImageType.IsStandard &&
                        x.ImageCategory == ImageCategoryNew.Drilling)
            .Select(x => new ImageDto
            {
                Id = x.Id,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                ImageType = _mapper.Map<ImageTypeDto>(x.ImageType),
                ImageSubtype = _mapper.Map<ImageSubtypeDto>(x.ImageSubtype),
                CroppedImages = _mapper.Map<List<ImageCropDto>>(x.CroppedImages),
                SegmentResult = x.SegmentResult,
                OcrResult = x.OcrResult
            })
            .OrderBy(x => x.DepthFrom)
            .ToListAsync();
        var wetImages = imageAll.Where(x => x.ImageSubtype is { IsWet: true }).ToList();
        var dryImages = imageAll.Where(x => x.ImageSubtype is { IsDry: true }).ToList();
        var images = wetImages.Count > 0 ? wetImages : dryImages;

        if (images == null || images.Count == 0)
        {
            throw new UserFriendlyException("No images found for the drill hole.");
        }

        var rowsByImage = new List<List<RowSegmentDto>>();
        foreach (var image in images)
        {
            var imageCrops = image.CroppedImages
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();

            var segmentResult =
                JsonConvert.DeserializeObject<List<SegmentResultRecoveryDto>>(image.SegmentResult ?? "[]")
                ?? [];
            var segmentCores = segmentResult.Where(x => x.Class == "core").ToList();

            var ocrResult = JsonConvert.DeserializeObject<List<OcrResultRecoveryDto>>(image.OcrResult ?? "[]")
                            ?? [];

            if (imageCrops.Count == 0 || segmentCores.Count == 0)
            {
                continue;
            }

            var rowSegments = RecoveryHandler.MergeDataRowSegmentOcr(imageCrops, segmentCores, ocrResult);

            for (var i = 0; i < imageCrops.Count; i++)
            {
                var intersectionPoints = imageCrops[i].RockLines
                    .Where(x => x.Type == RockLineType.Recovery)
                    .Select(x => new LineCoordinateDto
                    {
                        StartX = x.StartX + rowSegments[i].X,
                        EndX = x.EndX + rowSegments[i].X,
                    }).ToList();
                rowSegments[i].IntersectionPoints = intersectionPoints;
                rowSegments[i].DepthFrom = imageCrops[i].DepthFrom ?? 0;
                rowSegments[i].DepthTo = imageCrops[i].DepthTo ?? 0;
            }

            rowsByImage.Add(rowSegments);
        }

        var horizontalRows = RecoveryHandler.AlignSegmentsHorizontally(rowsByImage);
        var verticalRows = RecoveryHandler.AlignSegmentsVertical(rowsByImage);

        if (verticalRows == null || verticalRows.Count == 0)
        {
            throw new UserFriendlyException("No vertical row segments found for calculation.");
        }

        var firstRow = verticalRows.First();
        var lastRow = verticalRows.Last();

        var firstOcrBlock = new OcrBlockRecoveryDto
        {
            X = firstRow.X,
            Y = firstRow.Y,
            Width = 100,
            Height = 100,
            Value = firstRow.DepthFrom
        };
        var lastOcrBlock = new OcrBlockRecoveryDto
        {
            X = lastRow.X + lastRow.Width,
            Y = lastRow.Y,
            Width = 100,
            Height = 100,
            Value = lastRow.DepthTo
        };

        var horizontalOcrResults = horizontalRows
            .SelectMany(row => row.OcrResults)
            .Where(r => r is { Type: "wooden" or "Wooden" })
            // .GroupBy(r => r.Text)
            // .Select(g => g.Last())
            .ToList();

        var verticalOcrResults = verticalRows
            .SelectMany(row => row.OcrResults)
            .Where(r => r is { Type: "wooden" or "Wooden" })
            // .GroupBy(r => r.Text)
            // .Select(g => g.Last())
            .ToList();

        var horizontalOcrBlocks = horizontalOcrResults
            .Where(item => double.TryParse(item.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockRecoveryDto
            {
                X = item.X,
                OriginalX = item.OriginalX,
                Y = item.Y,
                Width = item.Width,
                Height = item.Height,
                Value = double.Parse(item.Text, CultureInfo.InvariantCulture),
                RowIndex = item.RowIndex,
                ImageCropId = item.ImageCropId
            })
            .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
            .ToList();
        var verticalOcrBlocks = verticalOcrResults
            .Where(item => double.TryParse(item.Text, NumberStyles.Any, CultureInfo.InvariantCulture, out _))
            .Select(item => new OcrBlockRecoveryDto
            {
                X = item.X,
                OriginalX = item.OriginalX,
                Y = item.Y,
                Width = item.Width,
                Height = item.Height,
                Value = double.Parse(item.Text, CultureInfo.InvariantCulture),
                RowIndex = item.RowIndex,
                ImageCropId = item.ImageCropId,
                id = item.Id
            })
            .Where(ob => ob.Value >= firstOcrBlock.Value && ob.Value <= lastOcrBlock.Value)
            .ToList();

        var fullHorizontalOcrBlocks = new List<OcrBlockRecoveryDto>();
        if (horizontalOcrBlocks.Count > 0 && horizontalOcrBlocks[0].Value > firstOcrBlock.Value)
        {
            fullHorizontalOcrBlocks.Add(firstOcrBlock);
        }

        fullHorizontalOcrBlocks.AddRange(horizontalOcrBlocks);
        if (horizontalOcrBlocks.Count > 0 && horizontalOcrBlocks[^1].Value < lastOcrBlock.Value)
        {
            fullHorizontalOcrBlocks.Add(lastOcrBlock);
        }

        var fullVerticalOcrBlocks = new List<OcrBlockRecoveryDto>();
        if (verticalOcrBlocks.Count > 0 && verticalOcrBlocks[0].Value > firstOcrBlock.Value)
        {
            fullVerticalOcrBlocks.Add(firstOcrBlock);
        }

        fullVerticalOcrBlocks.AddRange(verticalOcrBlocks);
        if (verticalOcrBlocks.Count > 0 && verticalOcrBlocks[^1].Value < lastOcrBlock.Value)
        {
            fullVerticalOcrBlocks.Add(lastOcrBlock);
        }

        var filterVerticalOcrBlocks = RecoveryHandler.FilterValidData(fullVerticalOcrBlocks);
        var filterHorizontalOcrBlocks = RecoveryHandler.FilterValidData(fullHorizontalOcrBlocks);

        var lengthRows = RecoveryHandler.SegmentLengthRows(horizontalRows, filterHorizontalOcrBlocks, coreTray);
        var resultDepths = RecoveryHandler.CalculateDepths(filterHorizontalOcrBlocks);
        var resultLengths = RecoveryHandler.CalculateLengths(lengthRows);

        if (filterVerticalOcrBlocks.Count != resultLengths.Count + 1 || resultDepths.Count != resultLengths.Count)
        {
            throw new UserFriendlyException("Mismatch between calculated segments and OCR blocks.");
        }

        var recoveryResults = new List<RecoveryResult>();
        for (var i = 0; i < resultLengths.Count; i++)
        {
            var recoveryPercentage = resultLengths[i] == 0 ? 0 : (resultLengths[i] / resultDepths[i]) * 100;

            var recoveryResult = new RecoveryResult
            {
                DrillHoleId = input.DrillHoleId,
                OcrValueFrom = filterVerticalOcrBlocks[i].Value,
                FromX = filterHorizontalOcrBlocks[i].OriginalX,
                FromY = filterHorizontalOcrBlocks[i].Y,
                FromImageCropId = filterHorizontalOcrBlocks[i].ImageCropId,
                FromRowIndex = filterVerticalOcrBlocks[i].RowIndex,
                FromOcrId = filterVerticalOcrBlocks[i].id ?? "",
                OcrValueTo = filterVerticalOcrBlocks[i + 1].Value,
                ToX = filterHorizontalOcrBlocks[i + 1].OriginalX,
                ToY = filterHorizontalOcrBlocks[i + 1].Y,
                ToImageCropId = filterHorizontalOcrBlocks[i + 1].ImageCropId,
                ToRowIndex = filterVerticalOcrBlocks[i + 1].RowIndex,
                ToOcrId = filterVerticalOcrBlocks[i + 1].id ?? "",
                Length = resultLengths[i],
                DepthInterval = resultDepths[i],
                Recovery = recoveryPercentage
            };

            recoveryResults.Add(recoveryResult);
        }

        var filteredItems = recoveryResults.Where(x => !x.OcrValueFrom.Equals(x.OcrValueTo)).ToList();
        foreach (var recoveryResult in filteredItems)
        {
            await _recoveryResultRepository.InsertAsync(recoveryResult);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();

        return filteredItems.Select(rr => _mapper.Map<RecoveryResultDto>(rr)).ToList();
    }

    /// <inheritdoc />
    public async Task<RecoveryResultDto> UpdateRecoveryResultAsync(UpdateRecoveryResultDto input)
    {
        var recoveryResult = await _recoveryResultRepository.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (recoveryResult == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.Id);
        }

        recoveryResult.OcrValueFrom = input.OcrValueFrom ?? recoveryResult.OcrValueFrom;
        recoveryResult.FromX = input.FromX ?? recoveryResult.FromX;
        recoveryResult.FromY = input.FromY ?? recoveryResult.FromY;
        recoveryResult.FromImageCropId = input.FromImageCropId ?? recoveryResult.FromImageCropId;
        recoveryResult.FromRowIndex = input.FromRowIndex ?? recoveryResult.FromRowIndex;
        recoveryResult.FromOcrId = input.FromOcrId ?? recoveryResult.FromOcrId;
        recoveryResult.OcrValueTo = input.OcrValueTo ?? recoveryResult.OcrValueTo;
        recoveryResult.ToX = input.ToX ?? recoveryResult.ToX;
        recoveryResult.ToY = input.ToY ?? recoveryResult.ToY;
        recoveryResult.ToImageCropId = input.ToImageCropId ?? recoveryResult.ToImageCropId;
        recoveryResult.ToRowIndex = input.ToRowIndex ?? recoveryResult.ToRowIndex;
        recoveryResult.ToOcrId = input.ToOcrId ?? recoveryResult.ToOcrId;
        recoveryResult.Length = input.Length ?? recoveryResult.Length;
        recoveryResult.Recovery = input.Recovery ?? recoveryResult.Recovery;
        recoveryResult.DepthInterval = input.DepthInterval ?? recoveryResult.DepthInterval;

        return _mapper.Map<RecoveryResultDto>(recoveryResult);
    }

    /// <inheritdoc />
    public async Task DeleteRecoveryResultAsync(EntityDto<int> input)
    {
        var recoveryResult = await _recoveryResultRepository.FirstOrDefaultAsync(x => x.Id == input.Id);
        if (recoveryResult == null)
        {
            throw new EntityNotFoundException(typeof(DrillHole), input.Id);
        }

        await _recoveryResultRepository.DeleteAsync(recoveryResult);
    }
}