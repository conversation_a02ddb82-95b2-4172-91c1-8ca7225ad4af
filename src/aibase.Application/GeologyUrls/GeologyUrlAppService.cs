using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using aibase.GeologyUrls.Dto;
using aibase.GeologyUrls.Services.GeologyUrlService;

namespace aibase.GeologyUrls;

/// <summary>
/// 
/// </summary>
public class GeologyUrlAppService : AsyncCrudAppService<GeologyUrl, GeologyUrlDto, int,
    PagedGeologyUrlResultRequestDto,
    CreateGeologyUrlDto, UpdateGeologyUrlDto>, IGeologyUrlAppService
{
    private readonly IGeologyUrlService _geologyUrlService;

    /// <inheritdoc />
    public GeologyUrlAppService(IRepository<GeologyUrl, int> repository,
        IGeologyUrlService geologyUrlService) : base(repository)
    {
        _geologyUrlService = geologyUrlService;
    }
    
    /// <inheritdoc />
    public override async Task<GeologyUrlDto> CreateAsync(CreateGeologyUrlDto input)
    {
        var geologyUrl = await _geologyUrlService.CreateAsync(input);
        return MapToEntityDto(geologyUrl);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<GeologyUrlDto>> GetAllAsync(PagedGeologyUrlResultRequestDto input)
    {
        return await _geologyUrlService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyUrlDto> GetAsync(EntityDto<int> input)
    {
        return await _geologyUrlService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyUrlDto> UpdateAsync(UpdateGeologyUrlDto input)
    {
        return await _geologyUrlService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public override async Task DeleteAsync(EntityDto<int> input)
    {
        await _geologyUrlService.DeleteAsync(input);
    }
}