using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyUrls.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyUrls.Services.GeologyUrlService;

/// <inheritdoc />
public class GeologyUrlService : IGeologyUrlService
{
    private readonly IRepository<GeologyUrl, int> _repository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public GeologyUrlService(IRepository<GeologyUrl, int> repository, IAbpSession abpSession, IMapper mapper)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<GeologyUrl> CreateAsync(CreateGeologyUrlDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyUrl =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyUrl != null)
        {
            if (returnExist)
            {
                return existingGeologyUrl;
            }
            
            throw new UserFriendlyException($"The Geology Url with the name {existingGeologyUrl.Name} already exists.");
        }
        
        var geologyUrl = new GeologyUrl()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyUrl);
        return geologyUrl;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyUrlDto>> GetAllAsync(PagedGeologyUrlResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var itemsDto = _mapper.Map<List<GeologyUrlDto>>(items);

        return new PagedResultDto<GeologyUrlDto>(totalCount, itemsDto);
    }

    /// <inheritdoc />
    public async Task<GeologyUrlDto> GetAsync(EntityDto<int> input)
    {
        var geologyUrl = await ValidateGeologyUrlEntity(input.Id);
        return _mapper.Map<GeologyUrlDto>(geologyUrl);
    }

    /// <inheritdoc />
    public async Task<GeologyUrlDto> UpdateAsync(UpdateGeologyUrlDto input)
    {
        var geologyUrl = await ValidateGeologyUrlEntity(input.Id);
        
        geologyUrl.Name = input.Name ?? geologyUrl.Name;
        geologyUrl.IsActive = input.IsActive ?? geologyUrl.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyUrl = await ValidateGeologyUrlEntity(input.Id);
        await _repository.DeleteAsync(geologyUrl);
    }

    private async Task<GeologyUrl> ValidateGeologyUrlEntity(int id)
    {
        var item = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (item == null)
        {
            throw new EntityNotFoundException(typeof(GeologyUrl), id);
        }

        return item;
    }
}