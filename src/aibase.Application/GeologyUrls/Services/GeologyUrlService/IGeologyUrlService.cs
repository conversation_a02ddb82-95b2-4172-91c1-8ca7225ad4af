using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.GeologyUrls.Dto;

namespace aibase.GeologyUrls.Services.GeologyUrlService;

/// <inheritdoc />
public interface IGeologyUrlService : ITransientDependency
{
    /// <summary>
    /// Creates a new GeologyUrl
    /// </summary>
    Task<GeologyUrl> CreateAsync(CreateGeologyUrlDto input, bool returnExist = false);

    /// <summary>
    /// Gets all GeologyUrls with paging and filtering
    /// </summary>
    Task<PagedResultDto<GeologyUrlDto>> GetAllAsync(PagedGeologyUrlResultRequestDto input);

    /// <summary>
    /// Gets a specific GeologyUrl by id
    /// </summary>
    Task<GeologyUrlDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Updates an existing GeologyUrl
    /// </summary>
    Task<GeologyUrlDto> UpdateAsync(UpdateGeologyUrlDto input);

    /// <summary>
    /// Deletes a GeologyUrl
    /// </summary>
    Task DeleteAsync(EntityDto<int> input);
}