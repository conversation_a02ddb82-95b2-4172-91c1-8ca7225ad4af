﻿using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using aibase.APIKeys;
using aibase.APIKeys.Services.ApiKeyAuthentication;
using aibase.APIKeys.Services.APIKeyService;
using aibase.Images.Services.AlignService;
using aibase.Images.Services.ImageService;
using aibase.Images.Services.ProcessService;
using aibase.Images.Services.UploadService;

namespace aibase.Images
{
    /// <summary>
    /// 
    /// </summary>
    public class ImageAppService : AsyncCrudAppService<Image, ImageDto, int, PagedImageResultRequestDto, CreateImageDto,
        ImageDto>, IImageAppService
    {
        private readonly IProcessService _processService;
        private readonly IUploadService _uploadService;
        private readonly IAlignService _alignService;
        private readonly IImageService _imageService;
        private readonly IApiKeyService _apiKeyService;

        /// <inheritdoc />
        public ImageAppService(
            IRepository<Image, int> repository,
            IProcessService processService,
            IUploadService uploadService,
            IAlignService alignService, 
            IImageService imageService, IApiKeyService apiKeyService) : base(repository)
        {
            _processService = processService;
            _uploadService = uploadService;
            _alignService = alignService;
            _imageService = imageService;
            _apiKeyService = apiKeyService;
        }

        /// <inheritdoc />
        [ApiKeyAuthorize]
        public override async Task<ImageDto> CreateAsync([FromForm] CreateImageDto input)
        {
            await _apiKeyService.CheckRoleApiKey(APIKeyCode.UploadImage);
            var image = await _uploadService.UploadImage(input);
            return MapToEntityDto(image);
        }

        /// <inheritdoc />
        [ApiKeyAuthorize]
        public override async Task<PagedResultDto<ImageDto>> GetAllAsync(PagedImageResultRequestDto input)
        {
            await _apiKeyService.CheckRoleApiKey(APIKeyCode.GetAllImage);
            var result = await _imageService.GetAllAsync(input);

            return result;
        }
        
        /// <inheritdoc />
        [ApiKeyAuthorize]
        public async Task<PagedResultDto<ImageViewDto>> GetAllByViewAsync(PagedImageViewRequestDto input)
        {
            await _apiKeyService.CheckRoleApiKey(APIKeyCode.GetAllImage);
            var result = await _imageService.GetAllByViewAsync(input);

            return result;
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public override async Task<ImageDto> GetAsync(EntityDto<int> input)
        {
            return await _imageService.GetAsync(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task<GetResultImageDto> GetResultImageAsync(int id)
        {
            return await _imageService.GetResultImageAsync(id);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task DeleteMultiAsync(string ids)
        {
            await _imageService.DeleteMultiAsync(ids);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task<List<string>> ValidateImageNameAsync(ValidateDto validateDto)
        {
            return await _imageService.ValidateImageNameAsync(validateDto);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task<string> UploadImgAsync([FromForm] UploadImageDto upload)
        {
            return await _imageService.UploadImgAsync(upload);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task<PagedResultDto<ImageCropDto>> CropImgAsync(CropCoordinateDto input)
        {
            return await _imageService.CropImgAsync(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task UpdateStatusAsync(int id, int status)
        {
            await _imageService.UpdateStatusAsync(id, status);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task UpdateResultOcrSegmentAsync(UpdateResultOcrSegment input)
        {
            await _imageService.UpdateResultOcrSegmentAsync(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task UpdateImageAsync(UpdateImageDto input)
        {
            await _imageService.UpdateImageAsync(input);
        }

        /// <inheritdoc />
        [ApiKeyAuthorize]
        public async Task<IActionResult> ProcessImageAsync(ProcessImageDto input)
        {
            try
            {
                await _apiKeyService.CheckRoleApiKey(APIKeyCode.ProcessImage);
                var result = await _processService.ProcessImageV2(input);
                return new OkObjectResult(result);
            }
            catch (ArgumentException ex)
            {
                return new BadRequestObjectResult(ex.Message);
            }
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task AlignPolygonAsync(AlignPolygonDto input)
        {
            await _alignService.AlignRows(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task ConfirmDrillholeOcrAsync(ConfirmDrillholeOcrDto input)
        {
            await _imageService.ConfirmDrillholeOcrAsync(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task CheckWorkflowOcrAINamingAsync(CheckWorkflowOcr input)
        {
            await _imageService.CheckWorkflowOcrAiNamingAsync(input);
        }

        /// <inheritdoc />
        [AbpAuthorize]
        public async Task<int> GetSkipCountByDepthAsync(PagedImageResultRequestDto input)
        {
            return await _imageService.GetSkipCountByDepthAsync(input);
        }

        /// <inheritdoc />
        [ApiKeyAuthorize]
        public async Task<PagedResultDto<DetailByRowDto>> GetDetailByRowAsync(PagedDetailByRowResultRequestDto input)
        {
            await _apiKeyService.CheckRoleApiKey(APIKeyCode.GetImageRowData);
            return await _imageService.GetDetailByRowAsync(input);
        }

        /// <inheritdoc />
        public async Task MockDataAsync()
        {
            await _imageService.MockDataAsync();
        }

        /// <inheritdoc />
        [ApiExplorerSettings(IgnoreApi = true)]
        public override async Task DeleteAsync(EntityDto<int> input)
        {
            await base.DeleteAsync(input);
        }
        
        /// <inheritdoc />
        [ApiExplorerSettings(IgnoreApi = true)]
        public override Task<ImageDto> UpdateAsync(ImageDto input)
        {
            return base.UpdateAsync(input);
        }
    }
}