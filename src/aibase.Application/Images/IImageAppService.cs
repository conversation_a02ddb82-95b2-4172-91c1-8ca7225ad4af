using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using aibase.ImageCrops.Dto;
using aibase.Images.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Images;

/// <inheritdoc />
public interface IImageAppService : IAsyncCrudAppService<ImageDto, int, PagedImageResultRequestDto, CreateImageDto,
    ImageDto>
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetResultImageDto> GetResultImageAsync(int id);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<ImageViewDto>> GetAllByViewAsync(PagedImageViewRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task DeleteMultiAsync(string ids);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="validateDto"></param>
    /// <returns></returns>
    Task<List<string>> ValidateImageNameAsync(ValidateDto validateDto);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="upload"></param>
    /// <returns></returns>
    Task<string> UploadImgAsync([FromForm] UploadImageDto upload);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<ImageCropDto>> CropImgAsync(CropCoordinateDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    Task UpdateStatusAsync(int id, int status);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateResultOcrSegmentAsync(UpdateResultOcrSegment input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateImageAsync(UpdateImageDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<IActionResult> ProcessImageAsync(ProcessImageDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task AlignPolygonAsync(AlignPolygonDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ConfirmDrillholeOcrAsync(ConfirmDrillholeOcrDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CheckWorkflowOcrAINamingAsync(CheckWorkflowOcr input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> GetSkipCountByDepthAsync(PagedImageResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DetailByRowDto>> GetDetailByRowAsync(PagedDetailByRowResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task MockDataAsync();
    
}