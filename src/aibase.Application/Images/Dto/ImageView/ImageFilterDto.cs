﻿using System;
using Abp.Application.Services.Dto;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using AutoMapper;
using System.Collections.Generic;
using aibase.DrillHoles.Dto;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.Images.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(Image))]
    public class ImageFilterDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public int ImageTypeId { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public int? ImageSubtypeId { get; set; }
    }
}