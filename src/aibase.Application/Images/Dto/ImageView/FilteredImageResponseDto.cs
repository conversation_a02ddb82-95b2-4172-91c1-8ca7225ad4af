using System.Collections.Generic;
using aibase.DrillHoles.Dto;

namespace aibase.Images.Dto;

/// <summary>
/// Response DTO for filtered image retrieval with structured format
/// </summary>
public class FilteredImageResponseDto
{
    /// <summary>
    /// List of images matching the filter criteria
    /// </summary>
    public List<ImageViewDto> List { get; set; } = [];

    /// <summary>
    /// Drill hole information
    /// </summary>
    public DrillHoleInfo DrillHole { get; set; } = new();

    /// <summary>
    /// Image type identifier
    /// </summary>
    public int TypeId { get; set; }

    /// <summary>
    /// Image type name
    /// </summary>
    public string TypeName { get; set; } = string.Empty;

    /// <summary>
    /// Image subtype identifier (optional)
    /// </summary>
    public int? SubTypeId { get; set; }

    /// <summary>
    /// Image subtype name (optional)
    /// </summary>
    public string? SubTypeName { get; set; }
}

/// <summary>
/// Drill hole information for the filtered response
/// </summary>
public class DrillHoleInfo
{
    /// <summary>
    /// Drill hole name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Drill hole identifier
    /// </summary>
    public int Id { get; set; }
}
