﻿using System;
using Abp.Application.Services.Dto;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using AutoMapper;
using System.Collections.Generic;
using aibase.DrillHoles.Dto;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes.Dto;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.Images.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(Image))]
    public class ImageViewDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public List<FileViewDto> files { get; set; } = [];
        
        /// <summary>
        /// 
        /// </summary>
        public DrillHoleViewDto DrillHole { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProjectViewDto Project { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ProspectViewDto Prospect { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double DepthFrom { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public double DepthTo { get; set; }


        /// <summary>
        /// 
        /// </summary>
        public List<ImageCropViewDto> CroppedImages { get; set; } = [];

        /// <summary>
        /// 
        /// </summary>
        public string? CreatedByUser { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public string? CreatedByName { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageStatus? ImageStatus { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public DateTime CreationTime { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageCategoryNew ImageCategory { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageTypeViewDto? ImageType { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public ImageSubtypeViewDto? ImageSubtype { get; set; }
        
    }
}