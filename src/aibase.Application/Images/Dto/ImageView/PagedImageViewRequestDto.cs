using Abp.Application.Services.Dto;
using aibase.ImageEntity;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace aibase.Images.Dto;

/// <summary>
/// 
/// </summary>
public class PagedImageViewRequestDto : PagedResultRequestDto, IValidatableObject
{
    /// <summary>
    ///
    /// </summary>
    public string? HoleIds { get; set; }

        
    /// <summary>
    ///
    /// </summary>
    public string? DrillHoleNames { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public ImageCategoryNew? ImageCategory { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageTypeId { get; set; }
        
    /// <summary>
    /// 
    /// </summary>
    public int? ImageSubtypeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<ImageFilterDto>? ImageFilterDto { get; set; }

    /// <inheritdoc />
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (string.IsNullOrWhiteSpace(HoleIds) && string.IsNullOrWhiteSpace(DrillHoleNames))
        {
            yield return new ValidationResult(
                "Either HoleIds or DrillHoleNames must be provided.",
                new[] { nameof(HoleIds), nameof(DrillHoleNames) });
        }
    }
}