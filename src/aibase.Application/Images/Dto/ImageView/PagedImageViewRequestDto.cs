using Abp.Application.Services.Dto;
using aibase.ImageEntity;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace aibase.Images.Dto;

/// <summary>
/// Request DTO for image view operations with enhanced filtering capabilities
/// </summary>
public class PagedImageViewRequestDto : PagedResultRequestDto, IValidatableObject
{
    /// <summary>
    /// Drill hole identifiers (JSON serialized array or single ID)
    /// </summary>
    public string? HoleIds { get; set; }

    /// <summary>
    /// Drill hole names (JSON serialized array)
    /// </summary>
    public string? DrillHoleNames { get; set; }

    /// <summary>
    /// Image category filter
    /// </summary>
    public ImageCategoryNew? ImageCategory { get; set; }

    /// <summary>
    /// List of image filters for multi-filter operations
    /// Each filter contains type (required) and subtype (optional)
    /// </summary>
    public List<ImageFilterDto> ImageFilterDto { get; set; } = [];

    /// <inheritdoc />
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Validate that either HoleIds or DrillHoleNames is provided
        if (string.IsNullOrWhiteSpace(HoleIds) && string.IsNullOrWhiteSpace(DrillHoleNames))
        {
            yield return new ValidationResult(
                "Either HoleIds or DrillHoleNames must be provided.",
                new[] { nameof(HoleIds), nameof(DrillHoleNames) });
        }
    }
}