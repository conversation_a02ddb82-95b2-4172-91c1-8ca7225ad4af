using Abp.Application.Services.Dto;
using aibase.ImageEntity;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace aibase.Images.Dto;

/// <summary>
/// Request DTO for image view operations with enhanced filtering capabilities
/// </summary>
public class PagedImageViewRequestDto : PagedResultRequestDto, IValidatableObject
{
    /// <summary>
    /// Drill hole identifiers (JSON serialized array or single ID)
    /// </summary>
    public string? HoleIds { get; set; }

    /// <summary>
    /// Drill hole names (JSON serialized array)
    /// </summary>
    public string? DrillHoleNames { get; set; }

    /// <summary>
    /// Image category filter
    /// </summary>
    public ImageCategoryNew? ImageCategory { get; set; }

    /// <summary>
    /// Single image type identifier (for backward compatibility)
    /// </summary>
    public int? ImageTypeId { get; set; }

    /// <summary>
    /// Single image subtype identifier (for backward compatibility)
    /// </summary>
    public int? ImageSubtypeId { get; set; }

    /// <summary>
    /// List of image filters for multi-filter operations
    /// Each filter contains type (required) and subtype (optional)
    /// </summary>
    public List<ImageFilterDto>? ImageFilterDto { get; set; }

    /// <inheritdoc />
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        // Validate that either HoleIds or DrillHoleNames is provided
        if (string.IsNullOrWhiteSpace(HoleIds) && string.IsNullOrWhiteSpace(DrillHoleNames))
        {
            yield return new ValidationResult(
                "Either HoleIds or DrillHoleNames must be provided.",
                new[] { nameof(HoleIds), nameof(DrillHoleNames) });
        }

        // Validate ImageFilterDto if provided
        if (ImageFilterDto != null && ImageFilterDto.Any())
        {
            for (int i = 0; i < ImageFilterDto.Count; i++)
            {
                var filter = ImageFilterDto[i];
                if (filter.ImageTypeId <= 0)
                {
                    yield return new ValidationResult(
                        $"ImageFilterDto[{i}].ImageTypeId must be a positive integer.",
                        new[] { $"ImageFilterDto[{i}].ImageTypeId" });
                }
            }
        }

        // Validate that either single filters or ImageFilterDto is used, not both
        if (ImageFilterDto != null && ImageFilterDto.Any() &&
            (ImageTypeId.HasValue || ImageSubtypeId.HasValue))
        {
            yield return new ValidationResult(
                "Cannot use both single filter properties (ImageTypeId, ImageSubtypeId) and ImageFilterDto simultaneously. Use either the single filter approach or the ImageFilterDto list.",
                new[] { nameof(ImageFilterDto), nameof(ImageTypeId), nameof(ImageSubtypeId) });
        }
    }
}