﻿using Abp.Application.Services.Dto;
using aibase.ProjectEntity;
using AutoMapper;
using System.Collections.Generic;
using aibase.AssaySuites.Dto;
using aibase.GeologySuites.Dto;
using aibase.GeotechSuites.Dto;
using aibase.ImageTypes.Dto;
using aibase.LoggingViews;
using aibase.MobileProfiles.Dto;
using aibase.Polygons.Dto;
using aibase.RockGroups.Dto;
using aibase.RqdCalculations.Dto;
using aibase.Suites.Dto;
using aibase.Workflows.Dto;

namespace aibase.Projects.Dto;

/// <inheritdoc />
[AutoMap(typeof(Project))]
public class ProjectViewDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    public string Name { get; set; } = string.Empty;
        
}
