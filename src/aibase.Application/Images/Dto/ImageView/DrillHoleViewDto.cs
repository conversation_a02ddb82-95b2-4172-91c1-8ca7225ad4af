﻿using System;
using System.Collections.Generic;
using Abp.Application.Services.Dto;
using aibase.DownholeSurveys.Dto;
using aibase.DrillHoleEntity;
using AutoMapper;
using aibase.Projects.Dto;
using aibase.Prospects.Dto;

namespace aibase.DrillHoles.Dto
{
    /// <inheritdoc />
    [AutoMap(typeof(DrillHole))]
    public class DrillHoleViewDto : EntityDto
    {
        /// <summary>
        /// 
        /// </summary>
        public string Name { get; set; } = string.Empty;
    }
}