using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using aibase.Images.Dto;

namespace aibase.Images.Services;

/// <summary>
/// 
/// </summary>
public static class ImageCommon
{
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="idsJson"></param>
    /// <param name="paramName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static int[]? DeserializeIds(string? idsJson, string paramName)
    {
        if (string.IsNullOrEmpty(idsJson)) return null;
        try
        {
            // Try to deserialize as array first
            try
            {
                return JsonConvert.DeserializeObject<int[]>(idsJson);
            }
            catch
            {
                // If array deserialization fails, try as single integer
                var singleId = JsonConvert.DeserializeObject<int>(idsJson);
                return new[] { singleId };
            }
        }
        catch (JsonException ex)
        {
            throw new ArgumentException($"Invalid {paramName} format", paramName, ex);
        }
    }
    
    /// <summary>
    ///
    /// </summary>
    /// <param name="namesJson"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public static string[] DeserializeNames(string? namesJson)
    {
        if (string.IsNullOrEmpty(namesJson)) return [];
        try
        {
            return JsonConvert.DeserializeObject<string[]>(namesJson) ?? [];
        }
        catch (JsonException)
        {
            return [];
        }
    }

    /// <summary>
    /// Deserialize JSON string to ImageFilterDto array
    /// </summary>
    /// <param name="filtersJson">JSON string containing array of image filters</param>
    /// <param name="paramName">Parameter name for error reporting</param>
    /// <returns>Array of ImageFilterDto objects</returns>
    /// <exception cref="ArgumentException">Thrown when JSON format is invalid</exception>
    public static ImageFilterDto[] DeserializeImageFilters(string? filtersJson, string paramName)
    {
        if (string.IsNullOrEmpty(filtersJson)) return [];
        try
        {
            return JsonConvert.DeserializeObject<ImageFilterDto[]>(filtersJson) ?? [];
        }
        catch (JsonException ex)
        {
            throw new ArgumentException($"Invalid {paramName} format. Expected JSON array of image filters.", paramName, ex);
        }
    }
}