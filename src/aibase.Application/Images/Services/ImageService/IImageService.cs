using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.ImageCrops.Dto;
using aibase.Images.Dto;
using Microsoft.AspNetCore.Mvc;

namespace aibase.Images.Services.ImageService;

/// <inheritdoc />
public interface IImageService : ITransientDependency
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<ImageDto>> GetAllAsync(PagedImageResultRequestDto input);
    
    /// <summary>
    /// Get filtered images with structured response format for multiple drill holes and filters
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<List<FilteredImageResponseDto>> GetAllByViewAsync(PagedImageViewRequestDto input);

    /// <summary>
    /// Get filtered images with structured response format for multiple drill holes and filters
    /// </summary>
    /// <param name="input">Request containing drill hole identifiers and image filters</param>
    /// <returns>List of filtered image responses grouped by drill hole and filter</returns>
    Task<List<FilteredImageResponseDto>> GetAllByViewFilteredAsync(PagedImageViewRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ImageDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<GetResultImageDto> GetResultImageAsync(int id);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ids"></param>
    /// <returns></returns>
    Task DeleteMultiAsync(string ids);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="validateDto"></param>
    /// <returns></returns>
    Task<List<string>> ValidateImageNameAsync(ValidateDto validateDto);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="upload"></param>
    /// <returns></returns>
    Task<string> UploadImgAsync([FromForm] UploadImageDto upload);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<ImageCropDto>> CropImgAsync(CropCoordinateDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="id"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    Task UpdateStatusAsync(int id, int status);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateResultOcrSegmentAsync(UpdateResultOcrSegment input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task UpdateImageAsync(UpdateImageDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task ConfirmDrillholeOcrAsync(ConfirmDrillholeOcrDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CheckWorkflowOcrAiNamingAsync(CheckWorkflowOcr input);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<int> GetSkipCountByDepthAsync(PagedImageResultRequestDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<PagedResultDto<DetailByRowDto>> GetDetailByRowAsync(PagedDetailByRowResultRequestDto input);

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    Task MockDataAsync();
}