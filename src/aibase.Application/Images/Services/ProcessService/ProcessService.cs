﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.UI;
using aibase.DrillHoles.Services.DrillHoleService;
using aibase.ImageCrops;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.AlignService;
using aibase.Images.Services.CropService;
using aibase.Images.Services.RotateService;
using aibase.Images.Services.UploadService.Handler;
using aibase.Models.Dto;
using aibase.Models.Services.WorkflowService;
using aibase.ResultSteps;
using aibase.RockLines.Dto;
using aibase.RockLines.Services;
using aibase.StepWorkflowEntity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.Images.Services.ProcessService;

/// <inheritdoc />
public class ProcessService : IProcessService
{
    private readonly IRepository<Image, int> _repository;
    private readonly IRepository<StepWorkflow, int> _stepWorkflowRepository;
    private readonly IRepository<ResultStep, int> _resultStepRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ICropService _cropService;
    private readonly IRotateService _rotateService;
    private readonly IAlignService _alignService;
    private readonly IDrillHoleService _drillHoleService;
    private readonly IWorkflowService _workflowService;
    private readonly IRockLineService _rockLineService;

    /// <summary>
    /// 
    /// </summary>
    public ProcessService(
        IRepository<Image, int> repository,
        IRepository<StepWorkflow> stepWorkflowRepository,
        IRepository<ResultStep> resultStepRepository,
        IUnitOfWorkManager unitOfWorkManager,
        ICropService cropService,
        IRotateService rotateService,
        IAlignService alignService,
        IDrillHoleService drillHoleService,
        IWorkflowService workflowService,
        IRockLineService rockLineService)
    {
        _repository = repository;
        _stepWorkflowRepository = stepWorkflowRepository;
        _resultStepRepository = resultStepRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _cropService = cropService;
        _rotateService = rotateService;
        _alignService = alignService;
        _drillHoleService = drillHoleService;
        _workflowService = workflowService;
        _rockLineService = rockLineService;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="UserFriendlyException"></exception>
    public async Task<IActionResult> ProcessImageV2(ProcessImageDto input)
    {
        try
        {
            var steps = await _stepWorkflowRepository.GetAllIncluding(x => x.BoundingBox, x => x.BoundingRows)
                .AsNoTracking()
                .Where(x => x.WorkflowId == input.WorkflowId)
                .OrderBy(x => x.CreationTime)
                .ToListAsync();
            if (steps.Count == 0)
            {
                throw new UserFriendlyException("The workflow has not been configured with steps.");
            }

            var image = await _repository.GetAllIncluding(x => x.Files, x => x.CroppedImages)
                .FirstOrDefaultAsync(x => x.Id == input.ImageId);
            if (image == null)
            {
                throw new UserFriendlyException("The image has not been configured with steps.");
            }

            var sameDepthImages = await _repository.GetAll()
                .Where(x => x.DepthFrom == image.DepthFrom && x.DepthTo == image.DepthTo &&
                            x.DrillHoleId == image.DrillHoleId && x.Id != image.Id)
                .ToListAsync();

            var fileImg = image.Files.FirstOrDefault(x => x.Size == ImageConstSettings.ImageMediumSize) ??
                          image.Files.FirstOrDefault(x => x.Size == ImageConstSettings.ImageFullSize);

            if (fileImg == null)
            {
                throw new UserFriendlyException("This image currently does not have a URL path.");
            }

            var inputStep = fileImg.Url;

            var boundingBoxDeserialize =
                JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingBox ?? "[]") ?? [];
            var boundingRowDeserialize =
                JsonConvert.DeserializeObject<CropCoordinate[]>(image.BoundingRows ?? "[]") ?? [];

            var boundingBox = boundingBoxDeserialize.OrderBy(c => c.Y).ToArray();
            var boundingRow = boundingRowDeserialize.OrderBy(c => c.Y).ToArray();
            var cropCoordinates = boundingBox.Concat(boundingRow).OrderBy(c => c.Y).ToArray();

            foreach (var step in steps)
            {
                switch (step.ToolType)
                {
                    case ToolType.AssignBox:
                        boundingBox = JsonConvert
                            .DeserializeObject<CropCoordinate[]>(step.BoundingBox?.Coordinates ?? "[]")?
                            .OrderBy(c => c.Y).ToArray();
                        cropCoordinates = boundingBox?.Concat(boundingRow ?? []).OrderBy(c => c.Y).ToArray();
                        break;
                    case ToolType.AssignRow:
                        boundingRow = JsonConvert
                            .DeserializeObject<CropCoordinate[]>(step.BoundingRows?.Coordinates ?? "[]")?
                            .OrderBy(c => c.Y).ToArray();
                        cropCoordinates = boundingBox?.Concat(boundingRow ?? []).OrderBy(c => c.Y).ToArray();
                        break;
                }

                if (step.ToolType == ToolType.Crop)
                {
                    var check = steps.Select(x => x.ToolType).Contains(ToolType.AssignBox);
                    var cropResult = await HandleCropStep(step, input, inputStep, cropCoordinates ?? [],
                        image.DrillHoleId,
                        check);

                    // Crop additional image type
                    await CropAdditionalImageTypeAsync(step, image, sameDepthImages,
                        cropCoordinates ?? []);

                    // Rock Line
                    var segmentResult =
                        JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]")?.ToList() ??
                        [];
                    var segmentDetailResult =
                        JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentDetailResult ?? "[]")?.ToList() ??
                        [];
                    var autoInsertRockLineDto = new AutoInsertRockLineDto
                    {
                        Segments = segmentResult,
                        SegmentDetails = segmentDetailResult,
                        ImageCrops = cropResult,
                        DrillHoleId = image.DrillHoleId
                    };
                    await _rockLineService.AutoInsertRockLineAsync(autoInsertRockLineDto);
                }

                switch (step.ToolType)
                {
                    case ToolType.RotateClockwise or ToolType.RotateCounterClockwise:
                        await _rotateService.RotateImg(image, step.DataValue, cropCoordinates ?? [], step.ToolType);
                        break;
                    case ToolType.Align:
                    {
                        var alignPolygon = new AlignPolygonDto
                        {
                            Id = input.ImageId,
                            Coordinate = null
                        };
                        await _alignService.AlignRows(alignPolygon);
                        break;
                    }
                }

                switch (step.ModelId)
                {
                    case AiModelType.PolygonCropOcrSegment: // 8
                    {
                        await HandlePolygonCropOcrSegmentStep(step, image, inputStep, input.ImageStatus);
                        // Crop additional image type
                        await CropAdditionalImageTypeAsync(step, image, sameDepthImages,
                            cropCoordinates ?? []);
                        break;
                    }
                    case AiModelType.PolygonCrop: // 11
                    {
                        await HandlePolygonCropStep(step, image, inputStep, input.ImageStatus);
                        // Crop additional image type
                        await CropAdditionalImageTypeAsync(step, image, sameDepthImages,
                            cropCoordinates ?? []);
                        break;
                    }
                    case AiModelType.PolygonCropOcrSegmentDetailCorePieces: // 13
                    {
                        await HandlePolygonCropOcrSegmentDetailCorePiecesStep(step, image, inputStep,
                            input.ImageStatus);
                        // Crop additional image type
                        await CropAdditionalImageTypeAsync(step, image, sameDepthImages,
                            cropCoordinates ?? []);
                        break;
                    }
                    case AiModelType.SegmentAutoCrop: // 14
                    {
                        await HandleSegmentAutoCropStepAsync(step, image, inputStep, input.ImageStatus);
                        // Crop additional image type
                        await CropAdditionalImageTypeAsync(step, image, sameDepthImages,
                            cropCoordinates ?? []);
                        break;
                    }
                    case AiModelType.ProcessCoreOutline: // 15
                    {
                        await HandleProcessCoreOutlineStepAsync(step, image, inputStep, input.ImageStatus);
                        break;
                    }
                    case AiModelType.SegmentCorePieces: // 16
                    {
                        await HandleSegmentCorePiecesStepAsync(step, image, inputStep, input.ImageStatus);
                        break;
                    }
                }
            }

            return new OkObjectResult(new { Message = "The workflow has been executed successfully" });
        }
        catch (UserFriendlyException ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
        catch (ArgumentException ex)
        {
            return new BadRequestObjectResult(ex.Message);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandlePolygonCropOcrSegmentStep(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result = await _workflowService.PerformPolygonCropOcrSegmentAsync(inputStep, image, imageStatusDone,
            step.Prompt, step.SegmentFlag);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandlePolygonCropOcrSegmentDetailCorePiecesStep(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result =
            await _workflowService.PerformPolygonCropOcrSegmentDetailCorePiecesAsync(inputStep, image, imageStatusDone,
                step.Prompt);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandleSegmentAutoCropStepAsync(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result = await _workflowService.PerformSegmentAutoCropAsync(inputStep, image, imageStatusDone);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandleProcessCoreOutlineStepAsync(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result = await _workflowService.PerformProcessCoreOutlineAsync(inputStep, image, imageStatusDone);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandleSegmentCorePiecesStepAsync(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result = await _workflowService.PerformSegmentCorePiecesAsync(inputStep, image, imageStatusDone);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task HandlePolygonCropStep(StepWorkflow step, Image image, string inputStep,
        ImageStatus? imageStatusDone)
    {
        var result = await _workflowService.PerformPolygonCropAsync(inputStep, image, imageStatusDone);

        await SaveResultStep(step, image.Id, inputStep, result);

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <returns></returns>
    public async Task<List<ImageCrop>> HandleCropStep(StepWorkflow step, ProcessImageDto input, string inputStep,
        CropCoordinate[] cropCoordinates, int drillHoleId, bool assignStep)
    {
        var imageCrops = await _cropService.CropImg(input.ImageId, cropCoordinates, true, assignStep);
        await SaveResultStep(step, input.ImageId, inputStep, imageCrops);

        await _unitOfWorkManager.Current.SaveChangesAsync();
        await _drillHoleService.UpdateTotalImageByDrillholeAsync(drillHoleId);
        return imageCrops;
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task SaveResultStep(StepWorkflow step, int imageId, string inputStep, object output)
    {
        var resultStep =
            await _resultStepRepository.FirstOrDefaultAsync(x => x.ImageId == imageId && x.StepId == step.Id);
        if (resultStep == null)
        {
            resultStep = new ResultStep
            {
                StepId = step.Id,
                ImageId = imageId,
                Input = inputStep,
                Output = JsonConvert.SerializeObject(output, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                })
            };
            await _resultStepRepository.InsertAsync(resultStep);
        }
        else
        {
            resultStep.Input = inputStep;
            resultStep.Output = JsonConvert.SerializeObject(output, new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            });
            await _resultStepRepository.UpdateAsync(resultStep);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task CropAdditionalImageTypeAsync(StepWorkflow stepWorkflow, Image originImage, List<Image> images,
        CropCoordinate[] cropCoordinates)
    {
        var imageTypeIds = stepWorkflow.ImageTypesAdditional ?? [];
        var imageSubtypeIds = stepWorkflow.ImageSubtypesAdditional ?? [];
        var filteredImages = images.Select(x => x).ToList();
        if (stepWorkflow.IsCropAdditional)
        {
            if (imageTypeIds is { Count: > 0 } || imageSubtypeIds is { Count: > 0 })
            {
                filteredImages = images
                    .Where(x => imageTypeIds.Contains((int)x.ImageTypeId) || imageSubtypeIds.Contains((int)x.ImageSubtypeId))
                    .ToList();
            }

            foreach (var image in filteredImages)
            {
                image.BoundingBox = originImage.BoundingBox;
                image.BoundingRows = originImage.BoundingRows;
                await _cropService.CropImg(image.Id, cropCoordinates ?? []);
            }
        }
    }
}