using System;
using System.Threading.Tasks;
using Abp.BackgroundJobs;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Runtime.Session;
using aibase.DataEntries.Dto;
using aibase.DataEntries.Services;
using Castle.Core.Logging;

namespace aibase.DataEntries.Services.CalculateLoggingBarJob
{
    /// <summary>
    /// Background job service for calculating logging bars asynchronously
    /// </summary>
    public class CalculateLoggingBarJob : AsyncBackgroundJob<CalculateLoggingBarDto>, ITransientDependency
    {
        private readonly IDataEntryService _dataEntryService;
        private readonly IUnitOfWorkManager _unitOfWorkManager;
        private readonly IAbpSession _abpSession;
        private readonly ILogger _logger;

        /// <summary>
        /// Constructor for CalculateLoggingBarJob
        /// </summary>
        /// <param name="dataEntryService">Data entry service for logging bar calculations</param>
        /// <param name="unitOfWorkManager">Unit of work manager for transaction handling</param>
        /// <param name="abpSession">ABP session for tenant context</param>
        /// <param name="logger">Logger for error handling and debugging</param>
        public CalculateLoggingBarJob(
            IDataEntryService dataEntryService,
            IUnitOfWorkManager unitOfWorkManager,
            IAbpSession abpSession,
            ILogger logger)
        {
            _dataEntryService = dataEntryService;
            _unitOfWorkManager = unitOfWorkManager;
            _abpSession = abpSession;
            _logger = logger;
        }

        /// <summary>
        /// Executes the logging bar calculation asynchronously
        /// </summary>
        /// <param name="args">CalculateLoggingBarDto containing the parameters for calculation</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public override async Task ExecuteAsync(CalculateLoggingBarDto args)
        {
            try
            {
                _logger.Info($"Starting logging bar calculation for GeologySuiteId: {args.GeologySuiteId}, DrillHoleId: {args.DrillHoleId}, ImageSubtypeId: {args.ImageSubtypeId}");

                using (var uow = _unitOfWorkManager.Begin())
                {
                    // Set the tenant context if available
                    if (_abpSession.TenantId.HasValue)
                    {
                        using (_unitOfWorkManager.Current.SetTenantId(_abpSession.TenantId))
                        {
                            await _dataEntryService.CalculateLoggingBarAsync(args);
                        }
                    }
                    else
                    {
                        await _dataEntryService.CalculateLoggingBarAsync(args);
                    }

                    await uow.CompleteAsync();
                }

                _logger.Info($"Successfully completed logging bar calculation for GeologySuiteId: {args.GeologySuiteId}, DrillHoleId: {args.DrillHoleId}");
            }
            catch (Exception ex)
            {
                _logger.Error($"Error occurred while calculating logging bar for GeologySuiteId: {args.GeologySuiteId}, DrillHoleId: {args.DrillHoleId}", ex);
                throw; // Re-throw to let Hangfire handle retry logic
            }
        }
    }
}
