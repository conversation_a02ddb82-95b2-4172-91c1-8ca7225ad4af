using System.ComponentModel.DataAnnotations;

namespace aibase.DataEntries.Dto;

/// <summary>
/// 
/// </summary>
public class CalculateLoggingBarDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int GeologySuiteId { get; set; }

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int DrillHoleId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public int ImageTypeId { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public int? ImageSubtypeId { get; set; }
}