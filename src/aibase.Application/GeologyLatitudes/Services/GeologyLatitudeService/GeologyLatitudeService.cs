using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyLatitudes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologyLatitudes.Services.GeologyLatitudeService;

/// <inheritdoc />
public class GeologyLatitudeService : IGeologyLatitudeService
{
    private readonly IRepository<GeologyLatitude, int> _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public GeologyLatitudeService(
        IRepository<GeologyLatitude, int> repository,
        IAbpSession abpSession,
        IMapper mapper,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _repository = repository;
        _abpSession = abpSession;
        _mapper = mapper;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <inheritdoc />
    public async Task<GeologyLatitude> CreateAsync(CreateGeologyLatitudeDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }
        var tenantId = _abpSession.GetTenantId();

        var existingGeologyLatitude =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologyLatitude != null)
        {
            if (returnExist)
            {
                return existingGeologyLatitude;
            }
            
            throw new UserFriendlyException($"The Geology Latitude with the name {existingGeologyLatitude.Name} already exists.");
        }
        
        var geologyLatitude = new GeologyLatitude()
        {
            Name = input.Name,
            IsActive = input.IsActive,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologyLatitude);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return geologyLatitude;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologyLatitudeDto>> GetAllAsync(PagedGeologyLatitudeResultRequestDto input)
    {
        var query = _repository.GetAll()
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var items = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var itemsDto = _mapper.Map<List<GeologyLatitudeDto>>(items);

        return new PagedResultDto<GeologyLatitudeDto>(totalCount, itemsDto);
    }

    /// <inheritdoc />
    public async Task<GeologyLatitudeDto> GetAsync(EntityDto<int> input)
    {
        var geologyLatitude = await ValidateGeologyLatitudeEntity(input.Id);
        return _mapper.Map<GeologyLatitudeDto>(geologyLatitude);
    }

    /// <inheritdoc />
    public async Task<GeologyLatitudeDto> UpdateAsync(UpdateGeologyLatitudeDto input)
    {
        var geologyLatitude = await ValidateGeologyLatitudeEntity(input.Id);
        
        geologyLatitude.Name = input.Name ?? geologyLatitude.Name;
        geologyLatitude.IsActive = input.IsActive ?? geologyLatitude.IsActive;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologyLatitude = await ValidateGeologyLatitudeEntity(input.Id);
        await _repository.DeleteAsync(geologyLatitude);
    }

    private async Task<GeologyLatitude> ValidateGeologyLatitudeEntity(int id)
    {
        var item = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (item == null)
        {
            throw new EntityNotFoundException(typeof(GeologyLatitude), id);
        }

        return item;
    }
}