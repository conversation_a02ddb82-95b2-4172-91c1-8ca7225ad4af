using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Dependency;
using aibase.GeologyLatitudes.Dto;

namespace aibase.GeologyLatitudes.Services.GeologyLatitudeService;

/// <inheritdoc />
public interface IGeologyLatitudeService : ITransientDependency
{
    /// <summary>
    /// Creates a new GeologyLatitude
    /// </summary>
    Task<GeologyLatitude> CreateAsync(CreateGeologyLatitudeDto input, bool returnExist = false);

    /// <summary>
    /// Gets all GeologyLatitudes with paging and filtering
    /// </summary>
    Task<PagedResultDto<GeologyLatitudeDto>> GetAllAsync(PagedGeologyLatitudeResultRequestDto input);

    /// <summary>
    /// Gets a specific GeologyLatitude by id
    /// </summary>
    Task<GeologyLatitudeDto> GetAsync(EntityDto<int> input);

    /// <summary>
    /// Updates an existing GeologyLatitude
    /// </summary>
    Task<GeologyLatitudeDto> UpdateAsync(UpdateGeologyLatitudeDto input);

    /// <summary>
    /// Deletes a GeologyLatitude
    /// </summary>
    Task DeleteAsync(EntityDto<int> input);
}