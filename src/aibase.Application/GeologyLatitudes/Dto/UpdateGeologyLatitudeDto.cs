using System.ComponentModel.DataAnnotations;
using Abp.Application.Services.Dto;

namespace aibase.GeologyLatitudes.Dto;

/// <inheritdoc />
public class UpdateGeologyLatitudeDto : EntityDto
{
    /// <summary>
    /// 
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    [Required]
    public bool? IsActive { get; set; }
}