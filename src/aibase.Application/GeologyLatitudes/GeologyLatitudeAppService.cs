using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Domain.Repositories;
using aibase.GeologyLatitudes.Dto;
using aibase.GeologyLatitudes.Services.GeologyLatitudeService;

namespace aibase.GeologyLatitudes;

/// <summary>
/// 
/// </summary>
public class GeologyLatitudeAppService : AsyncCrudAppService<GeologyLatitude, GeologyLatitudeDto, int,
    PagedGeologyLatitudeResultRequestDto,
    CreateGeologyLatitudeDto, UpdateGeologyLatitudeDto>, IGeologyLatitudeAppService
{
    private readonly IGeologyLatitudeService _geologyLatitudeService;

    /// <inheritdoc />
    public GeologyLatitudeAppService(IRepository<GeologyLatitude, int> repository,
        IGeologyLatitudeService geologyLatitudeService) : base(repository)
    {
        _geologyLatitudeService = geologyLatitudeService;
    }
    
    /// <inheritdoc />
    public override async Task<GeologyLatitudeDto> CreateAsync(CreateGeologyLatitudeDto input)
    {
        var geologyCheckbox = await _geologyLatitudeService.CreateAsync(input);
        return MapToEntityDto(geologyCheckbox);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<GeologyLatitudeDto>> GetAllAsync(PagedGeologyLatitudeResultRequestDto input)
    {
        return await _geologyLatitudeService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyLatitudeDto> GetAsync(EntityDto<int> input)
    {
        return await _geologyLatitudeService.GetAsync(input);
    }

    /// <inheritdoc />
    public override async Task<GeologyLatitudeDto> UpdateAsync(UpdateGeologyLatitudeDto input)
    {
        return await _geologyLatitudeService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public override async Task DeleteAsync(EntityDto<int> input)
    {
        await _geologyLatitudeService.DeleteAsync(input);
    }
}