using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Linq.Extensions;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto.CalculationDepth;
using aibase.DrillHoles.Services.CalculationDepthService;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.UploadService.Handler;
using aibase.RockLines.Dto;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace aibase.RockLines.Services;

/// <inheritdoc />
public class RockLineService : IRockLineService
{
    private readonly IRepository<RockLine, int> _rockLineRepository;
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<DrillHole, int> _drillholeRepository;
    private readonly ICalculationDepthService _calculationDepthService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;


    /// <summary>
    /// 
    /// </summary>
    public RockLineService(
        IRepository<RockLine> rockLineRepository,
        IRepository<Image, int> imageRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IRepository<DrillHole, int> drillholeRepository,
        ICalculationDepthService calculationDepthService)
    {
        _rockLineRepository = rockLineRepository;
        _imageRepository = imageRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _drillholeRepository = drillholeRepository;
        _calculationDepthService = calculationDepthService;
    }

    /// <inheritdoc />
    public async Task<RockLine> CreateAsync(CreateRockLineDto input)
    {
        var rockLine = new RockLine
        {
            Type = input.Type,
            DepthFrom = input.DepthFrom,
            DepthTo = input.DepthTo,
            StartX = input.StartX,
            StartY = input.StartY,
            EndX = input.EndX,
            EndY = input.EndY,
            ImageCropId = input.ImageCropId,
            RowIndex = input.RowIndex
        };

        await _rockLineRepository.InsertAsync(rockLine);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return rockLine;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<RockLineDto>> GetAllAsync(PagedRockLineResultRequestDto input)
    {
        var query = _rockLineRepository.GetAll();

        if (input.ImageCropId.HasValue)
        {
            query = query.Where(x => x.ImageCropId == input.ImageCropId.Value);
        }

        if (input.Type.HasValue)
        {
            query = query.Where(x => x.Type == input.Type.Value);
        }

        var totalCount = await query.CountAsync();

        var items = await query
            .OrderBy(x => x.Id)
            .PageBy(input)
            .Select(x => new RockLineDto
            {
                Id = x.Id,
                Type = x.Type,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                StartX = x.StartX,
                StartY = x.StartY,
                EndX = x.EndX,
                EndY = x.EndY,
                ImageCropId = x.ImageCropId,
                RowIndex = x.RowIndex,
                LinkWidth = x.LinkWidth
            })
            .ToListAsync();

        return new PagedResultDto<RockLineDto>(totalCount, items);
    }

    /// <inheritdoc />
    public async Task<RockLineDto> UpdateAsync(UpdateRockLineDto input)
    {
        var rockLine = await _rockLineRepository.GetAsync(input.Id);

        rockLine.Type = input.Type ?? rockLine.Type;
        rockLine.DepthFrom = input.DepthFrom ?? rockLine.DepthFrom;
        rockLine.DepthTo = input.DepthTo ?? rockLine.DepthTo;
        rockLine.StartX = input.StartX ?? rockLine.StartX;
        rockLine.StartY = input.StartY ?? rockLine.StartY;
        rockLine.EndX = input.EndX ?? rockLine.EndX;
        rockLine.EndY = input.EndY ?? rockLine.EndY;
        rockLine.ImageCropId = input.ImageCropId ?? rockLine.ImageCropId;
        rockLine.RowIndex = input.RowIndex ?? rockLine.RowIndex;

        await _rockLineRepository.UpdateAsync(rockLine);

        return new RockLineDto
        {
            Id = rockLine.Id,
            Type = rockLine.Type,
            DepthFrom = rockLine.DepthFrom,
            DepthTo = rockLine.DepthTo,
            StartX = rockLine.StartX,
            StartY = rockLine.StartY,
            EndX = rockLine.EndX,
            EndY = rockLine.EndY,
            ImageCropId = rockLine.ImageCropId,
            RowIndex = rockLine.RowIndex,
            LinkWidth = rockLine.LinkWidth
        };
    }

    /// <inheritdoc />
    public async Task<RockLineDto> GetAsync(EntityDto<int> input)
    {
        var rockLine = await _rockLineRepository.GetAsync(input.Id);
        return new RockLineDto
        {
            Id = rockLine.Id,
            Type = rockLine.Type,
            DepthFrom = rockLine.DepthFrom,
            DepthTo = rockLine.DepthTo,
            StartX = rockLine.StartX,
            StartY = rockLine.StartY,
            EndX = rockLine.EndX,
            EndY = rockLine.EndY,
            ImageCropId = rockLine.ImageCropId,
            RowIndex = rockLine.RowIndex,
            LinkWidth = rockLine.LinkWidth
        };
    }

    /// <inheritdoc />
    public async Task AutoInsertRockLineAsync(AutoInsertRockLineDto input)
    {
        if (input.Segments.Count > 0 && input.SegmentDetails.Count > 0 && input.ImageCrops.Count != 0)
        {
            var imageCropRows = input.ImageCrops
                .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                .OrderBy(x => x.DepthFrom)
                .ToList();

            await _rockLineRepository.DeleteAsync(x => imageCropRows.Select(crop => crop.Id).Contains(x.ImageCropId));

            var segmentsByRow = input.Segments
                .Where(x => x.Class == "core")
                .GroupBy(segment => segment.rowIndex)
                .ToDictionary(group => group.Key, group => group.ToList());

            var segmentDetailsByRow = input.SegmentDetails
                .Where(x => x.Class == "core")
                .GroupBy(segment => segment.rowIndex)
                .ToDictionary(group => group.Key, group => group.ToList());

            // Prepare all rock lines before DB operations
            var rockLinesToInsert = new List<RockLine>();

            for (var i = 0; i < imageCropRows.Count; i++)
            {
                var coordinate = imageCropRows[i].Coordinate;
                if (coordinate != null)
                {
                    var boundingRows = JsonConvert.DeserializeObject<CoordinateBoundingDto>(coordinate);
                    if (boundingRows == null) continue;

                    // Calculate the y-coordinate of the horizontal line
                    var midY = boundingRows.Y + (boundingRows.Height / 2);

                    if (segmentsByRow.TryGetValue(i, out var matchingSegments))
                    {
                        foreach (var segment in matchingSegments)
                        {
                            // Calculate intersection points of the horizontal line with segment box
                            var intersections =
                                CalculateIntersections(segment.points, midY, boundingRows.X, boundingRows.Y);

                            // Ensure we have at least two intersection points
                            if (intersections.Count < 2) continue;

                            // Sort intersections by X to get Start and End points
                            var orderedIntersections = intersections.OrderBy(p => p.X).ToList();
                            rockLinesToInsert.Add(new RockLine
                            {
                                Type = RockLineType.Recovery,
                                StartX = orderedIntersections[0].X,
                                StartY = orderedIntersections[0].Y,
                                EndX = orderedIntersections[1].X - 1,
                                EndY = orderedIntersections[1].Y,
                                ImageCropId = imageCropRows[i].Id,
                                RowIndex = i
                            });
                        }
                    }

                    if (segmentDetailsByRow.TryGetValue(i, out var matchingSegmentDetails))
                    {
                        foreach (var segment in matchingSegmentDetails)
                        {
                            // Calculate intersection points of the horizontal line with segment box
                            var intersections =
                                CalculateIntersections(segment.points, midY, boundingRows.X, boundingRows.Y);

                            // Ensure we have at least two intersection points
                            if (intersections.Count < 2) continue;

                            // Sort intersections by X to get Start and End points
                            var orderedIntersections = intersections.OrderBy(p => p.X).ToList();

                            rockLinesToInsert.Add(new RockLine
                            {
                                Type = RockLineType.Rqd,
                                StartX = orderedIntersections[0].X,
                                StartY = orderedIntersections[0].Y,
                                EndX = orderedIntersections[1].X - 1,
                                EndY = orderedIntersections[1].Y,
                                ImageCropId = imageCropRows[i].Id,
                                RowIndex = i
                            });
                        }
                    }
                }
            }

            // Calc Depth Rock line
            var drillHole = await _drillholeRepository.GetAllIncluding(x => x.Project)
                                .AsNoTracking()
                                .Select(x => new { x.Id, x.Project.CoreTrayLength })
                                .FirstOrDefaultAsync(x => x.Id == input.DrillHoleId)
                            ?? throw new EntityNotFoundException(typeof(DrillHole), input.DrillHoleId);
            var coreTray = drillHole.CoreTrayLength;

            // Standard
            var standardImages = await _imageRepository.GetAllIncluding(x => x.ImageType)
                .AsNoTracking()
                .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsStandard)
                .OrderBy(x => x.DepthFrom)
                .Select(x => new ImageViewCalcDepthDto
                {
                    Id = x.Id,
                    DepthFrom = x.DepthFrom,
                    DepthTo = x.DepthTo,
                    OcrResult = x.OcrResult,
                    SegmentResult = x.SegmentResult,
                    CroppedImages = x.CroppedImages
                        .Where(c => c.Type == ImageConstSettings.ImageCropRow ||
                                    c.Type == ImageConstSettings.ImageCropRowLower)
                        .OrderBy(c => c.DepthFrom)
                        .Select(c => new CroppedImageViewCalcDepthDto
                        {
                            Id = c.Id,
                            Coordinate = c.Coordinate,
                            Type = c.Type,
                            DepthFrom = c.DepthFrom
                        })
                        .ToList()
                })
                .ToListAsync();

            // Standard
            var rigImages = await _imageRepository.GetAllIncluding(x => x.ImageType)
                .AsNoTracking()
                .Where(x => x.DrillHoleId == input.DrillHoleId && x.ImageType != null && x.ImageType.IsRig)
                .OrderBy(x => x.DepthFrom)
                .Select(x => new ImageViewCalcDepthDto
                {
                    Id = x.Id,
                    DepthFrom = x.DepthFrom,
                    DepthTo = x.DepthTo,
                    OcrResult = x.OcrResult,
                    SegmentResult = x.SegmentResult,
                    CroppedImages = x.CroppedImages
                        .Where(c => c.Type == ImageConstSettings.ImageCropRow ||
                                    c.Type == ImageConstSettings.ImageCropRowLower)
                        .OrderBy(c => c.DepthFrom)
                        .Select(c => new CroppedImageViewCalcDepthDto
                        {
                            Id = c.Id,
                            Coordinate = c.Coordinate,
                            Type = c.Type,
                            DepthFrom = c.DepthFrom
                        })
                        .ToList()
                })
                .ToListAsync();

            if (standardImages.Count == 0 && rigImages.Count == 0)
            {
                throw new UserFriendlyException("No images found for the drill hole.");
            }

            // Batch insert all rock lines
            var calculateLengthRockLineDto = new CalculateLengthRockLineDto
            {
                RockLines = rockLinesToInsert,
                Images = standardImages.Count > 0 ? standardImages : rigImages,
                CoreTray = coreTray
            };
            await CalculateLengthRockLineAsync(calculateLengthRockLineDto);
        }
    }

    /// <inheritdoc />
    public async Task CalculateLengthRockLineAsync(CalculateLengthRockLineDto input)
    {
        var rockLines = input.RockLines;
        // Batch insert all rock lines
        foreach (var rockLine in rockLines)
        {
            // Get the minimum StartX from all rock lines in the first row
            var startPoint = new CalculateDepthInListImageDto
            {
                Images = input.Images,
                ImageCropId = rockLine.ImageCropId,
                X = rockLine.StartX + 20,
                CoreTray = input.CoreTray
            };

            // Get the maximum EndX from all rock lines in the last row
            var endPoint = new CalculateDepthInListImageDto
            {
                Images = input.Images,
                ImageCropId = rockLine.ImageCropId,
                X = rockLine.EndX - 20,
                CoreTray = input.CoreTray
            };

            // Calculate the depth for start and end points sequentially to avoid DbContext concurrency issues
            var startDepth = _calculationDepthService.CalculateDepthInListImage(startPoint);
            var endDepth = _calculationDepthService.CalculateDepthInListImage(endPoint);

            if (startDepth == null || endDepth == null)
            {
                continue;
            }

            rockLine.DepthFrom = (double)startDepth;
            rockLine.DepthTo = (double)endDepth;
            rockLine.Length = (double)(endDepth - startDepth);
        }

        foreach (var rockLine in rockLines)
        {
            await _rockLineRepository.InsertOrUpdateAsync(rockLine);
        }
    }

    /// <inheritdoc />
    public async Task JoinRockLineAsync(JoinRockLineDto input)
    {
        var firstRockLine = await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == input.FirstRockLineId);
        var secondRockLine = await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == input.SecondRockLineId);

        if (firstRockLine == null || secondRockLine == null)
        {
            throw new UserFriendlyException("First or second rock line not found");
        }

        // Check if firstRockLine is already linked to another RockLine
        if (firstRockLine.LinkWidth.HasValue)
        {
            var oldLinkedRockLine =
                await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == firstRockLine.LinkWidth.Value);
            if (oldLinkedRockLine != null)
            {
                // Clear the old link
                oldLinkedRockLine.LinkWidth = null;
                await _rockLineRepository.UpdateAsync(oldLinkedRockLine);
            }
        }

        // Check if secondRockLine is already linked to another RockLine
        if (secondRockLine.LinkWidth.HasValue)
        {
            var oldLinkedRockLine =
                await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == secondRockLine.LinkWidth.Value);
            if (oldLinkedRockLine != null)
            {
                // Clear the old link
                oldLinkedRockLine.LinkWidth = null;
                await _rockLineRepository.UpdateAsync(oldLinkedRockLine);
            }
        }

        // Create new link between firstRockLine and secondRockLine
        firstRockLine.LinkWidth = secondRockLine.Id;
        secondRockLine.LinkWidth = firstRockLine.Id;

        // Update both RockLines in the repository
        await _rockLineRepository.UpdateAsync(firstRockLine);
        await _rockLineRepository.UpdateAsync(secondRockLine);
    }

    /// <inheritdoc />
    public async Task RemoveJoinRockLineAsync(JoinRockLineDto input)
    {
        var firstRockLine = await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == input.FirstRockLineId);
        var secondRockLine = await _rockLineRepository.FirstOrDefaultAsync(x => x.Id == input.SecondRockLineId);

        if (firstRockLine != null)
        {
            firstRockLine.LinkWidth = null;
        }

        if (secondRockLine != null)
        {
            secondRockLine.LinkWidth = null;
        }
    }

    private static List<(double X, double Y)> CalculateIntersections(List<List<double>> points, double midY,
        double boundingX,
        double boundingY)
    {
        var intersections = new List<(double X, double Y)>();

        // Convert points to a list of line segments (closed loop)
        for (var i = 0; i < points.Count; i++)
        {
            var p1 = points[i];
            var p2 = points[(i + 1) % points.Count]; // Connect to the next point, looping back to the first

            double x1 = p1[0], y1 = p1[1];
            double x2 = p2[0], y2 = p2[1];

            // Check if the line segment intersects with the horizontal line at midY
            if ((y1 <= midY && y2 > midY) || (y2 <= midY && y1 > midY))
            {
                // Calculate the x-coordinate of the intersection
                var t = (midY - y1) / (y2 - y1);
                var x = x1 + t * (x2 - x1);

                // Adjust coordinates relative to bounding box
                intersections.Add((x - boundingX, midY - boundingY));
            }
        }

        return intersections;
    }
}