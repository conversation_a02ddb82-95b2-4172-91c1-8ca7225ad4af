using System.Collections.Generic;
using aibase.DrillHoles.Dto.CalculationDepth;

namespace aibase.RockLines.Dto;

/// <summary>
/// 
/// </summary>
public class CalculateLengthRockLineDto
{
    /// <summary>
    /// 
    /// </summary>
    public List<RockLine> RockLines { get; set; } = [];
    
    /// <summary>
    /// 
    /// </summary>
    public double CoreTray { get; set; }
    
    /// <summary>
    /// 
    /// </summary>
    public List<ImageViewCalcDepthDto> Images { get; set; } = [];
}