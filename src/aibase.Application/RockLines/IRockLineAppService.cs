using System.Threading.Tasks;
using Abp.Application.Services;
using aibase.RockLines.Dto;

namespace aibase.RockLines;

/// <summary>
/// Application service interface for managing RockLines
/// </summary>
public interface IRockLineAppService : IAsyncCrudAppService<
    RockLineDto,           // DTO for the RockLine
    int,                   // Primary key type
    PagedRockLineResultRequestDto,    // Used for paging/filtering
    CreateRockLineDto,     // Used for creation
    UpdateRockLineDto>     // Used for updating
{
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task JoinRockLineAsync(JoinRockLineDto input);
    
    /// <summary>
    /// 
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task RemoveJoinRockLineAsync(JoinRockLineDto input);
}