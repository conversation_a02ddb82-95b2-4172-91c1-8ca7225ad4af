using System.Threading.Tasks;
using Abp.Application.Services;
using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using aibase.RockLines.Dto;
using aibase.RockLines.Services;
using Microsoft.AspNetCore.Mvc;

namespace aibase.RockLines;

/// <summary>
/// 
/// </summary>
[AbpAuthorize]
public class RockLineAppService : AsyncCrudAppService<RockLine, RockLineDto, int, PagedRockLineResultRequestDto,
    CreateRockLineDto, UpdateRockLineDto>, IRockLineAppService
{
    private readonly IRockLineService _rockLineService;

    /// <inheritdoc />
    public RockLineAppService(
        IRepository<RockLine> repository,
        IRockLineService rockLineService) : base(repository)
    {
        _rockLineService = rockLineService;
    }

    /// <inheritdoc />
    public override async Task<RockLineDto> CreateAsync(CreateRockLineDto input)
    {
        var rockLine = await _rockLineService.CreateAsync(input);
        return MapToEntityDto(rockLine);
    }

    /// <inheritdoc />
    public override async Task<PagedResultDto<RockLineDto>> GetAllAsync(PagedRockLineResultRequestDto input)
    {
        return await _rockLineService.GetAllAsync(input);
    }

    /// <inheritdoc />
    public override async Task<RockLineDto> UpdateAsync(UpdateRockLineDto input)
    {
        return await _rockLineService.UpdateAsync(input);
    }

    /// <inheritdoc />
    public async Task JoinRockLineAsync(JoinRockLineDto input)
    {
        await _rockLineService.JoinRockLineAsync(input);
    }

    /// <inheritdoc />
    [method:HttpPut]
    public async Task RemoveJoinRockLineAsync(JoinRockLineDto input)
    {
        await _rockLineService.RemoveJoinRockLineAsync(input);
    }

    /// <inheritdoc />
    public override async Task<RockLineDto> GetAsync(EntityDto<int> input)
    {
        return await _rockLineService.GetAsync(input);
    }
}