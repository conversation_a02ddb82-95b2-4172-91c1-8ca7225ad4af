using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Auditing;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.Colours;
using aibase.Colours.Dto;
using aibase.Colours.Services.ColourService;
using aibase.GeologyDates;
using aibase.GeologyDates.Dto;
using aibase.GeologyDates.Services.GeologyDateService;
using aibase.GeologyDescriptions;
using aibase.GeologyDescriptions.Dto;
using aibase.GeologyDescriptions.Services.GeologyDescriptionService;
using aibase.GeologyFields;
using aibase.GeologyFields.Dto;
using aibase.GeologyFields.Services.GeologyFieldService;
using aibase.GeologySuiteFields.Dto;
using aibase.GeologySuiteFields.Services.GeologySuiteFieldService;
using aibase.GeologySuites;
using aibase.GeologySuites.Dto;
using aibase.GeologySuites.Services.GeologySuiteService;
using aibase.Images.Services.AzureService;
using aibase.MultiTenancy;
using aibase.Numbers;
using aibase.Numbers.Dto;
using aibase.Numbers.Services;
using aibase.PickLists;
using aibase.PickLists.Dto;
using aibase.PickLists.Services.PickListService;
using aibase.RockGroups;
using aibase.RockGroups.Dto;
using aibase.RockGroups.Services.AssignService;
using aibase.RockGroups.Services.RockGroupService;
using aibase.RockStyles;
using aibase.RockStyles.Dto;
using aibase.RockStyles.Services;
using aibase.RockTypes;
using aibase.RockTypes.Dto;
using aibase.RockTypes.Services.RockTypeService;
using aibase.SettingAccounts.Dto;
using aibase.Settings;
using aibase.Units;
using aibase.Units.Dto;
using aibase.Units.Services.UnitService;
using aibase.WorkRoles;
using aibase.WorkRoles.Dto;
using aibase.WorkRoles.Services;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;
using ValueType = aibase.Numbers.ValueType;

namespace aibase.SettingAccounts.Services.SettingAccountService;

/// <inheritdoc />
public class SettingAccountService : ISettingAccountService
{
    private readonly IRepository<Setting, int> _repository;
    private readonly IRepository<WorkRole, int> _workRoleRepository;
    private readonly IRepository<GeologySuite, int> _geologySuiteRepository;
    private readonly IRepository<GeologyField, int> _geologyFieldRepository;
    private readonly IRepository<RockGroup, int> _rockGroupRepository;
    private readonly IRepository<RockType, int> _rockTypeRepository;
    private readonly IRepository<RockStyle, int> _rockStyleRepository;
    private readonly IRepository<Colour, int> _colourRepository;
    private readonly IRepository<Number, int> _numberRepository;
    private readonly IRepository<Unit, int> _unitRepository;
    private readonly IRepository<PickList, int> _pickListRepository;
    private readonly IRepository<GeologyDate, int> _geologyDateRepository;
    private readonly IRepository<GeologyDescription, int> _geologyDescriptionRepository;
    private readonly IRepository<AuditLog, long> _auditLogRepository;
    private readonly TenantManager _tenantManager;
    private readonly IRockStyleService _rockStyleService;
    private readonly IRockTypeService _rockTypeService;
    private readonly IRockGroupService _rockGroupService;
    private readonly IAssignService _assignRockTypeService;
    private readonly IGeologyFieldService _geologyFieldService;
    private readonly IGeologySuiteService _geologySuiteService;
    private readonly IWorkRoleService _workRoleService;
    private readonly IColourService _colourService;
    private readonly IUnitService _unitService;
    private readonly INumberService _numberService;
    private readonly IGeologyDateService _geologyDateService;
    private readonly IGeologyDescriptionService _geologyDescriptionService;
    private readonly IPickListService _pickListService;
    private readonly IGeologySuiteFieldService _geologySuiteFieldService;
    private readonly IAzureService _azureService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;


    /// <summary>
    /// 
    /// </summary>
    public SettingAccountService(
        IRepository<Setting, int> repository,
        IRepository<WorkRole, int> workRoleRepository,
        IRepository<GeologySuite, int> geologySuiteRepository,
        IRepository<GeologyField, int> geologyFieldRepository,
        IRepository<RockGroup, int> rockGroupRepository,
        IRepository<RockType, int> rockTypeRepository,
        IRepository<Colour, int> colourRepository,
        IRepository<Number, int> numberRepository,
        IRepository<Unit, int> unitRepository,
        IRepository<PickList, int> pickListRepository,
        IRepository<GeologyDate, int> geologyDateRepository,
        IRepository<GeologyDescription, int> geologyDescriptionRepository,
        IRepository<RockStyle, int> rockStyleRepository,
        IRepository<AuditLog, long> auditLogRepository,
        TenantManager tenantManager,
        IAzureService azureService,
        IRockStyleService rockStyleService,
        IRockTypeService rockTypeService,
        IRockGroupService rockGroupService,
        IGeologyFieldService geologyFieldService,
        IGeologySuiteService geologySuiteService,
        IWorkRoleService workRoleService,
        IAssignService assignRockTypeService,
        IColourService colourService,
        IUnitService unitService,
        INumberService numberService,
        IGeologyDateService geologyDateService,
        IGeologyDescriptionService geologyDescriptionService,
        IPickListService pickListService,
        IGeologySuiteFieldService geologySuiteFieldService,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _workRoleRepository = workRoleRepository;
        _geologySuiteRepository = geologySuiteRepository;
        _geologyFieldRepository = geologyFieldRepository;
        _rockGroupRepository = rockGroupRepository;
        _rockTypeRepository = rockTypeRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _azureService = azureService;
        _rockStyleService = rockStyleService;
        _rockTypeService = rockTypeService;
        _rockGroupService = rockGroupService;
        _geologyFieldService = geologyFieldService;
        _geologySuiteService = geologySuiteService;
        _workRoleService = workRoleService;
        _colourRepository = colourRepository;
        _numberRepository = numberRepository;
        _unitRepository = unitRepository;
        _pickListRepository = pickListRepository;
        _geologyDateRepository = geologyDateRepository;
        _geologyDescriptionRepository = geologyDescriptionRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _assignRockTypeService = assignRockTypeService;
        _colourService = colourService;
        _unitService = unitService;
        _numberService = numberService;
        _geologyDateService = geologyDateService;
        _geologyDescriptionService = geologyDescriptionService;
        _pickListService = pickListService;
        _rockStyleRepository = rockStyleRepository;
        _auditLogRepository = auditLogRepository;
        _tenantManager = tenantManager;
        _geologySuiteFieldService = geologySuiteFieldService;
    }

    /// <inheritdoc />
    public async Task<SettingAccountDto> UpdateAsync(SettingAccountDto input)
    {
        var setting = await ValidateSettingEntity(input.Id);

        setting.ProductName = input.ProductName;
        setting.Units = input.Units;
        setting.UnitsSymbol = input.UnitsSymbol;
        setting.ImageType = input.ImageType;
        setting.CollectionName = input.CollectionName;
        setting.CollectionNameSingular = input.CollectionNameSingular;
        setting.BoundingPolygonType = input.BoundingPolygonType;
        setting.RowPolygonType = input.RowPolygonType;
        setting.ViewWithBoundingPolygon = input.ViewWithBoundingPolygon;
        setting.RowPolygon = input.RowPolygon;
        setting.LogoProduct = input.LogoProduct;
        setting.isUseLogo = input.isUseLogo;
        setting.ImageStatus = input.ImageStatus;
        setting.ProjectIds = input.ProjectIds ?? null;
        setting.ProspectIds = input.ProspectIds ?? null;
        setting.DrillholeIds = input.DrillholeIds ?? null;
        setting.DrillHoleView = input.DrillHoleView ?? DrillHoleView.Original;
        setting.TenantId = input.TenantId;

        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            var tenant = await _tenantManager.GetByIdAsync(input.TenantId);
            tenant.Name = input.ProductName;
        }

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<SettingAccountDto> GetAsync(EntityDto<int> input)
    {
        var setting = await ValidateSettingEntity(input.Id);
        return _mapper.Map<SettingAccountDto>(setting);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<SettingAccountDto>> GetAllAsync(PagedSettingAccountResultRequestDto input)
    {
        var query = _repository.GetAll()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && (x.ProductName.ToLower().Contains(input.Keyword.ToLower()) ||
                                               x.CollectionName.ToLower().Contains(input.Keyword.ToLower())))
            .OrderByDescending(r => r.CreationTime);

        var totalCount = await query.CountAsync();

        var setting = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<SettingAccountDto>(totalCount, _mapper.Map<List<SettingAccountDto>>(setting));
    }

    /// <inheritdoc />
    public async Task<SettingAccountDto> GetDetailSettingAsync()
    {
        var tenant = _abpSession.GetTenantId();
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var settingDto = await _repository.FirstOrDefaultAsync(x => x.TenantId == tenant);

        await _repository.UpdateAsync(settingDto);

        return _mapper.Map<SettingAccountDto>(settingDto);
    }

    /// <inheritdoc />
    public async Task<string> ExportGeologyConfigAsync()
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var workRoleQuery = await _workRoleRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.WorkRoleSuites)
            .ThenInclude(x => x.GeologySuite)
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();
        var workRoles = workRoleQuery
            .SelectMany(entry => entry.WorkRoleSuites, (entry, style) => new WorkRoleExportDto()
            {
                Name = entry.Name,
                Description = entry.Description,
                IsActive = entry.IsActive,
                GeologySuite = style.GeologySuite.Name,
            })
            .ToList();

        var geologySuiteQuery = await _geologySuiteRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.GeologySuiteFields)
            .ThenInclude(x => x.GeologyField)
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();
        var geologySuites = geologySuiteQuery
            .SelectMany(entry => entry.GeologySuiteFields, (entry, style) => new GeologySuiteExportDto()
            {
                Name = entry.Name,
                IsActive = entry.IsActive,
                GeologyFieldName = style.Name,
                GeologyField = style.GeologyField.Name,
                GeologyFieldSequence = style.Sequence,
                GeologyFieldIsActive = style.IsActive,
                BackgroundColour = style.BackgroundColour,
                TextColour = style.TextColour
            })
            .ToList();

        var geologyFieldQuery = _geologyFieldService.QueryGeologyField();
        var geologyFields = await geologyFieldQuery.ToListAsync();

        var rockGroupQuery = await _rockGroupRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.RockGroupRockTypes)
            .ThenInclude(x => x.RockType)
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();
        var rockGroups = rockGroupQuery
            .SelectMany(entry => entry.RockGroupRockTypes, (entry, style) => new RockGroupExportDto()
            {
                Name = entry.Name,
                IsActive = entry.IsActive,
                RockTypeName = style.RockType.Name,
            })
            .ToList();

        var queryRockType = await _rockTypeRepository.GetAllIncluding(x => x.RockStyle)
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();
        var rockTypes = _mapper.Map<List<RockTypeDto>>(queryRockType);

        var rockStyles = await _rockStyleRepository.GetAll()
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var colours = await _colourRepository.GetAll()
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var numbers = await _numberRepository.GetAllIncluding(x => x.Unit)
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var units = await _unitRepository.GetAll()
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var geologyDates = await _geologyDateRepository.GetAll()
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var geologyDescriptions = await _geologyDescriptionRepository.GetAll()
            .AsNoTracking()
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();

        var pickListQuery = await _pickListRepository.GetAll()
            .AsNoTracking()
            .Include(x => x.PickListItems)
            .Where(r => r.TenantId == _abpSession.GetTenantId())
            .ToListAsync();
        var pickLists = pickListQuery
            .SelectMany(entry => entry.PickListItems, (entry, style) => new PickListExportDto()
            {
                Name = entry.Name,
                IsActive = entry.IsActive,
                ItemName = style.Name,
                ItemSequnce = style.Sequence,
                ItemIsActive = style.IsActive
            })
            .ToList();

        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        using var package = new ExcelPackage();

        var worksheetRockType = package.Workbook.Worksheets.Add("RockType");
        worksheetRockType.Cells[1, 1].Value = "Name";
        worksheetRockType.Cells[1, 2].Value = "Code";
        worksheetRockType.Cells[1, 3].Value = "Description";
        worksheetRockType.Cells[1, 4].Value = "IsActive";
        worksheetRockType.Cells[1, 5].Value = "Drawing Style";
        for (var i = 0; i < rockTypes.Count; i++)
        {
            var row = rockTypes[i];
            worksheetRockType.Cells[i + 2, 1].Value = row.Name;
            worksheetRockType.Cells[i + 2, 2].Value = row.Code;
            worksheetRockType.Cells[i + 2, 3].Value = row.Description;
            worksheetRockType.Cells[i + 2, 4].Value = row.IsActive;
            worksheetRockType.Cells[i + 2, 5].Value = row.RockStyle.Name;
        }

        var worksheetRockGroup = package.Workbook.Worksheets.Add("RockGroup");
        worksheetRockGroup.Cells[1, 1].Value = "Name";
        worksheetRockGroup.Cells[1, 2].Value = "IsActive";
        worksheetRockGroup.Cells[1, 3].Value = "RockType";
        for (var i = 0; i < rockGroups.Count; i++)
        {
            var row = rockGroups[i];
            worksheetRockGroup.Cells[i + 2, 1].Value = row.Name;
            worksheetRockGroup.Cells[i + 2, 2].Value = row.IsActive;
            worksheetRockGroup.Cells[i + 2, 3].Value = row.RockTypeName;
        }

        var worksheetGeologyField = package.Workbook.Worksheets.Add("GeologyField");
        worksheetGeologyField.Cells[1, 1].Value = "Name";
        worksheetGeologyField.Cells[1, 2].Value = "Code";
        worksheetGeologyField.Cells[1, 3].Value = "Type";
        worksheetGeologyField.Cells[1, 4].Value = "Reference";
        worksheetGeologyField.Cells[1, 5].Value = "BackgroundColour";
        worksheetGeologyField.Cells[1, 6].Value = "TextColour";
        worksheetGeologyField.Cells[1, 7].Value = "IsActive";
        for (var i = 0; i < geologyFields.Count; i++)
        {
            var row = geologyFields[i];
            worksheetGeologyField.Cells[i + 2, 1].Value = row.Name;
            worksheetGeologyField.Cells[i + 2, 2].Value = row.Code;
            worksheetGeologyField.Cells[i + 2, 3].Value = row.Type;
            worksheetGeologyField.Cells[i + 2, 4].Value = GetReferenceName(row);
            worksheetGeologyField.Cells[i + 2, 5].Value = row.BackgroundColour;
            worksheetGeologyField.Cells[i + 2, 6].Value = row.TextColour;
            worksheetGeologyField.Cells[i + 2, 7].Value = row.IsActive;
        }

        var worksheetGeologySuite = package.Workbook.Worksheets.Add("GeologySuite");
        worksheetGeologySuite.Cells[1, 1].Value = "Name";
        worksheetGeologySuite.Cells[1, 2].Value = "IsActive";
        worksheetGeologySuite.Cells[1, 3].Value = "GeologyField_Name";
        worksheetGeologySuite.Cells[1, 4].Value = "GeologyField";
        worksheetGeologySuite.Cells[1, 5].Value = "GeologyField_Sequence";
        worksheetGeologySuite.Cells[1, 6].Value = "GeologyField_IsActive";
        worksheetGeologySuite.Cells[1, 7].Value = "BackgroundColour";
        worksheetGeologySuite.Cells[1, 8].Value = "TextColour";
        for (var i = 0; i < geologySuites.Count; i++)
        {
            var row = geologySuites[i];
            worksheetGeologySuite.Cells[i + 2, 1].Value = row.Name;
            worksheetGeologySuite.Cells[i + 2, 2].Value = row.IsActive;
            worksheetGeologySuite.Cells[i + 2, 3].Value = row.GeologyFieldName;
            worksheetGeologySuite.Cells[i + 2, 4].Value = row.GeologyField;
            worksheetGeologySuite.Cells[i + 2, 5].Value = row.GeologyFieldSequence;
            worksheetGeologySuite.Cells[i + 2, 6].Value = row.GeologyFieldIsActive;
            worksheetGeologySuite.Cells[i + 2, 7].Value = row.BackgroundColour;
            worksheetGeologySuite.Cells[i + 2, 8].Value = row.TextColour;
        }

        var worksheetWorkRole = package.Workbook.Worksheets.Add("WorkRole");
        worksheetWorkRole.Cells[1, 1].Value = "Name";
        worksheetWorkRole.Cells[1, 2].Value = "Description";
        worksheetWorkRole.Cells[1, 3].Value = "IsActive";
        worksheetWorkRole.Cells[1, 4].Value = "GeologySuite";
        for (var i = 0; i < workRoles.Count; i++)
        {
            var row = workRoles[i];
            worksheetWorkRole.Cells[i + 2, 1].Value = row.Name;
            worksheetWorkRole.Cells[i + 2, 2].Value = row.Description;
            worksheetWorkRole.Cells[i + 2, 3].Value = row.IsActive;
            worksheetWorkRole.Cells[i + 2, 4].Value = row.GeologySuite;
        }

        var worksheetColour = package.Workbook.Worksheets.Add("Colour");
        worksheetColour.Cells[1, 1].Value = "Name";
        worksheetColour.Cells[1, 2].Value = "HexCode";
        worksheetColour.Cells[1, 3].Value = "IsActive";
        for (var i = 0; i < colours.Count; i++)
        {
            var row = colours[i];
            worksheetColour.Cells[i + 2, 1].Value = row.Name;
            worksheetColour.Cells[i + 2, 2].Value = row.HexCode;
            worksheetColour.Cells[i + 2, 3].Value = row.IsActive;
        }

        var worksheetNumber = package.Workbook.Worksheets.Add("Number");
        worksheetNumber.Cells[1, 1].Value = "Name";
        worksheetNumber.Cells[1, 2].Value = "ValueType";
        worksheetNumber.Cells[1, 3].Value = "UpperLimit";
        worksheetNumber.Cells[1, 4].Value = "LowerLimit";
        worksheetNumber.Cells[1, 5].Value = "IsPercent";
        worksheetNumber.Cells[1, 6].Value = "IsActive";
        worksheetNumber.Cells[1, 7].Value = "Unit";
        for (var i = 0; i < numbers.Count; i++)
        {
            var row = numbers[i];
            worksheetNumber.Cells[i + 2, 1].Value = row.Name;
            worksheetNumber.Cells[i + 2, 2].Value = row.ValueType;
            worksheetNumber.Cells[i + 2, 3].Value = row.UpperLimit;
            worksheetNumber.Cells[i + 2, 4].Value = row.LowerLimit;
            worksheetNumber.Cells[i + 2, 5].Value = row.IsPercent;
            worksheetNumber.Cells[i + 2, 6].Value = row.IsActive;
            worksheetNumber.Cells[i + 2, 7].Value = row.Unit.Name;
        }

        var worksheetUnit = package.Workbook.Worksheets.Add("Unit");
        worksheetUnit.Cells[1, 1].Value = "Name";
        worksheetUnit.Cells[1, 2].Value = "Code";
        worksheetUnit.Cells[1, 3].Value = "IsActive";
        for (var i = 0; i < units.Count; i++)
        {
            var row = units[i];
            worksheetUnit.Cells[i + 2, 1].Value = row.Name;
            worksheetUnit.Cells[i + 2, 2].Value = row.Code;
            worksheetUnit.Cells[i + 2, 3].Value = row.IsActive;
        }

        var worksheetGeologyDate = package.Workbook.Worksheets.Add("GeologyDate");
        worksheetGeologyDate.Cells[1, 1].Value = "Name";
        worksheetGeologyDate.Cells[1, 2].Value = "IsActive";
        for (var i = 0; i < geologyDates.Count; i++)
        {
            var row = geologyDates[i];
            worksheetGeologyDate.Cells[i + 2, 1].Value = row.Name;
            worksheetGeologyDate.Cells[i + 2, 2].Value = row.IsActive;
        }

        var worksheetGeologyDescription = package.Workbook.Worksheets.Add("GeologyDescription");
        worksheetGeologyDescription.Cells[1, 1].Value = "Name";
        worksheetGeologyDescription.Cells[1, 2].Value = "FieldHeight";
        worksheetGeologyDescription.Cells[1, 3].Value = "IsActive";
        for (var i = 0; i < geologyDescriptions.Count; i++)
        {
            var row = geologyDescriptions[i];
            worksheetGeologyDescription.Cells[i + 2, 1].Value = row.Name;
            worksheetGeologyDescription.Cells[i + 2, 2].Value = row.FieldHeight;
            worksheetGeologyDescription.Cells[i + 2, 3].Value = row.IsActive;
        }

        var worksheetPickList = package.Workbook.Worksheets.Add("PickList");
        worksheetPickList.Cells[1, 1].Value = "Name";
        worksheetPickList.Cells[1, 2].Value = "IsActive";
        worksheetPickList.Cells[1, 3].Value = "Item_Name";
        worksheetPickList.Cells[1, 4].Value = "Item_Sequence";
        worksheetPickList.Cells[1, 5].Value = "Item_IsActive";
        for (var i = 0; i < pickLists.Count; i++)
        {
            var row = pickLists[i];
            worksheetPickList.Cells[i + 2, 1].Value = row.Name;
            worksheetPickList.Cells[i + 2, 2].Value = row.IsActive;
            worksheetPickList.Cells[i + 2, 3].Value = row.ItemName;
            worksheetPickList.Cells[i + 2, 4].Value = row.ItemSequnce;
            worksheetPickList.Cells[i + 2, 5].Value = row.ItemIsActive;
        }

        var worksheetRockStyle = package.Workbook.Worksheets.Add("DrawingStyles");
        worksheetRockStyle.Cells[1, 1].Value = "Name";
        worksheetRockStyle.Cells[1, 2].Value = "FillColor";
        worksheetRockStyle.Cells[1, 3].Value = "LineColor";
        worksheetRockStyle.Cells[1, 4].Value = "FillTexture";
        worksheetRockStyle.Cells[1, 5].Value = "FillTransparency";
        worksheetRockStyle.Cells[1, 6].Value = "LineStyle";
        worksheetRockStyle.Cells[1, 7].Value = "LineThickness";
        worksheetRockStyle.Cells[1, 8].Value = "IsActive";
        for (var i = 0; i < rockStyles.Count; i++)
        {
            var row = rockStyles[i];
            worksheetRockStyle.Cells[i + 2, 1].Value = row.Name;
            worksheetRockStyle.Cells[i + 2, 2].Value = row.FillColor;
            worksheetRockStyle.Cells[i + 2, 3].Value = row.LineColor;
            worksheetRockStyle.Cells[i + 2, 4].Value = row.FillTexture;
            worksheetRockStyle.Cells[i + 2, 5].Value = row.FillTransparency;
            worksheetRockStyle.Cells[i + 2, 6].Value = row.LineStyle;
            worksheetRockStyle.Cells[i + 2, 7].Value = row.LineThickness;
            worksheetRockStyle.Cells[i + 2, 8].Value = row.IsActive;
        }

        var excelData = await package.GetAsByteArrayAsync();
        await using var fileStream = new MemoryStream(excelData);
        var uploadedUrl = await _azureService.UploadFileAsync(fileStream,
            $"GeologyConfig/GeologyConfig-{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.xlsx", true);

        return uploadedUrl;
    }

    /// <inheritdoc />
    public async Task ImportGeologyConfigAsync(ImportGeologyConfigDto input)
    {
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        using var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);

        using var package = new ExcelPackage(stream);

        var rockStyles = ReadDataRockStyle(package);
        var rockTypes = ReadDataRockType(package);
        var rockGroups = ReadDataRockGroup(package);
        var geologyFields = ReadDataGeologyField(package);
        var geologySuites = ReadDataGeologySuite(package);
        var workRoles = ReadDataWorkRole(package);
        var colours = ReadDataColour(package);
        var units = ReadDataUnit(package);
        var numbers = ReadDataNumber(package);
        var geologyDates = ReadDataGeologyDate(package);
        var geologyDescriptions = ReadDataGeologyDescription(package);
        var pickLists = ReadDataPickList(package);

        await ImportDataRockStyleAsync(rockStyles);
        await ImportDataRockTypeAsync(rockTypes);
        await ImportDataRockGroupAsync(rockGroups);
        await ImportDataColourAsync(colours);
        await ImportDataUnitAsync(units);
        await ImportDataNumberAsync(numbers);
        await ImportDataGeologyDateAsync(geologyDates);
        await ImportDataGeologyDescriptionAsync(geologyDescriptions);
        await ImportDataPickListAsync(pickLists);
        await ImportDataGeologyFieldAsync(geologyFields);
        await ImportDataGeologySuiteAsync(geologySuites);
        await ImportDataWorkRoleAsync(workRoles);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<LoggingImageUploadMobileDto>> GetLoggingImageUpdateMobile(
        PagedLoggingImageUploadMobileResultRequestDto input)
    {
        // string[] devices = ["iPhone", "iPad", "Android"];

        var auditLogQuery = _auditLogRepository.GetAll()
            .AsNoTracking()
            .Where(x => x.ServiceName == "aibase.Images.ImageAppService" && x.MethodName == "CreateAsync")
            // .Where(x => devices.Any(device => x.BrowserInfo.Contains(device)))
            .OrderByDescending(x => x.ExecutionTime);

        var totalCount = await auditLogQuery.CountAsync();

        var auditLogsData = await auditLogQuery
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();
        
        var auditLogs = auditLogsData
            .Select(x =>
            {
                var inputDto = JsonSerializer.Deserialize<InputUploadImageDto>(x.Parameters);
                return new LoggingImageUploadMobileDto
                {
                    UserId = (long)x.UserId,
                    ExecutionTime = x.ExecutionTime,
                    Params = inputDto ?? new InputUploadImageDto(),
                    Success = x.ExceptionMessage == null,
                    ErrorInfo = x.ExceptionMessage
                };
            })
            .ToList();

        return new PagedResultDto<LoggingImageUploadMobileDto>(totalCount, auditLogs);
    }

    private static List<RockStyleDto> ReadDataRockStyle(ExcelPackage package)
    {
        var rockStyles = new List<RockStyleDto>();

        var worksheet = package.Workbook.Worksheets[11];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockStyle = new RockStyleDto
            {
                Name = worksheet.Cells[row, 1].Text,
                FillColor = worksheet.Cells[row, 2].Text,
                LineColor = worksheet.Cells[row, 3].Text,
                FillTexture = worksheet.Cells[row, 4].Text,
                FillTransparency = Convert.ToInt32(worksheet.Cells[row, 5].Text),
                LineStyle = worksheet.Cells[row, 6].Text,
                LineThickness = Convert.ToInt32(worksheet.Cells[row, 7].Text),
                IsActive = bool.Parse(worksheet.Cells[row, 8].Text),
            };

            rockStyles.Add(rockStyle);
        }

        return rockStyles;
    }

    private async Task ImportDataRockStyleAsync(List<RockStyleDto> rockStyles)
    {
        foreach (var createRockStyleDto in rockStyles.Select(rockStyle => new CreateRockStyleDto()
                 {
                     Name = rockStyle.Name,
                     FillColor = rockStyle.FillColor,
                     LineColor = rockStyle.LineColor,
                     FillTexture = rockStyle.FillTexture,
                     FillTransparency = rockStyle.FillTransparency,
                     LineStyle = rockStyle.LineStyle,
                     LineThickness = rockStyle.LineThickness,
                     IsActive = rockStyle.IsActive,
                 }))
        {
            await _rockStyleService.CreateAsync(createRockStyleDto, true);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    private static List<RockTypeDto> ReadDataRockType(ExcelPackage package)
    {
        var rockTypes = new List<RockTypeDto>();

        var worksheet = package.Workbook.Worksheets[0];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new RockTypeDto
            {
                Name = worksheet.Cells[row, 1].Text,
                Code = worksheet.Cells[row, 2].Text,
                Description = worksheet.Cells[row, 3].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 4].Text),
                RockStyle = new RockStyleDto
                {
                    Name = worksheet.Cells[row, 5].Text,
                },
            };

            rockTypes.Add(rockType);
        }

        return rockTypes;
    }

    private async Task ImportDataRockTypeAsync(List<RockTypeDto> rockTypes)
    {
        foreach (var rockType in rockTypes)
        {
            var rockStyle = await _rockStyleRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.Name == rockType.RockStyle.Name && x.TenantId == _abpSession.GetTenantId())
                .FirstOrDefaultAsync();
            if (rockStyle == null) continue;

            var createRockTypeDto = new CreateRockTypeDto()
            {
                Name = rockType.Name,
                Code = rockType.Code,
                Description = rockType.Description,
                IsActive = rockType.IsActive,
                RockStyleId = rockStyle.Id,
            };
            await _rockTypeService.CreateAsync(createRockTypeDto, true);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    private static List<RockGroupDto> ReadDataRockGroup(ExcelPackage package)
    {
        var rockGroupExports = new List<RockGroupExportDto>();

        var worksheet = package.Workbook.Worksheets[1];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new RockGroupExportDto
            {
                Name = worksheet.Cells[row, 1].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 2].Text),
                RockTypeName = worksheet.Cells[row, 3].Text,
            };

            rockGroupExports.Add(rockType);
        }

        var rockGroups = rockGroupExports
            .GroupBy(item => new { item.Name, item.IsActive })
            .Select(group => new RockGroupDto()
            {
                Name = group.Key.Name,
                IsActive = group.Key.IsActive,
                RockTypes = group.Select(g => new RockTypeDto() { Name = g.RockTypeName }).ToList()
            })
            .ToList();

        return rockGroups;
    }

    private async Task ImportDataRockGroupAsync(List<RockGroupDto> rockGroups)
    {
        foreach (var rockGroupDto in rockGroups)
        {
            var createRockGroupDto = new CreateRockGroupDto()
            {
                Name = rockGroupDto.Name,
                IsActive = rockGroupDto.IsActive,
            };

            var rockGroup = await _rockGroupService.CreateAsync(createRockGroupDto, true);

            List<int> rockTypeIds = [];
            if (rockGroupDto.RockTypes != null)
                foreach (var rockTypeDto in rockGroupDto.RockTypes)
                {
                    var rockType = await _rockTypeRepository.GetAll()
                        .AsNoTracking()
                        .Where(x => x.Name == rockTypeDto.Name && x.TenantId == _abpSession.GetTenantId())
                        .FirstOrDefaultAsync();

                    if (rockType != null) rockTypeIds.Add(rockType.Id);
                }

            var assignRockTypeGroupDto = new AssignRockTypeGroupDto
            {
                RockGroupId = rockGroup.Id,
                RockTypeIds = rockTypeIds
            };
            await _assignRockTypeService.AssignRockTypeAsync(assignRockTypeGroupDto);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    private static List<GeologyFieldImportDto> ReadDataGeologyField(ExcelPackage package)
    {
        var geologyFields = new List<GeologyFieldImportDto>();

        var worksheet = package.Workbook.Worksheets[2];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var typeString = worksheet.Cells[row, 3].Text;
            if (!Enum.TryParse(typeString, out FieldType fieldType))
            {
                fieldType = FieldType.Colour;
            }

            var rockType = new GeologyFieldImportDto
            {
                Name = worksheet.Cells[row, 1].Text,
                Code = worksheet.Cells[row, 2].Text,
                Type = fieldType,
                Reference = worksheet.Cells[row, 4].Text,
                BackgroundColour = worksheet.Cells[row, 5].Text,
                TextColour = worksheet.Cells[row, 6].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 7].Text),
            };

            geologyFields.Add(rockType);
        }

        return geologyFields;
    }

    private async Task ImportDataGeologyFieldAsync(List<GeologyFieldImportDto> geologyFieldImportsDto)
    {
        foreach (var geologyFieldImportDto in geologyFieldImportsDto)
        {
            var referenceId = await GetReferenceIdAsync(geologyFieldImportDto);
            var numberId = await GetNumberIdAsync(geologyFieldImportDto);
            var rockTypeId = await GetRockTypeIdAsync(geologyFieldImportDto);
            var rockGroupId = await GetRockGroupIdAsync(geologyFieldImportDto);
            var geologyField = new CreateGeologyFieldDto()
            {
                Name = geologyFieldImportDto.Name,
                Code = geologyFieldImportDto.Code,
                Type = geologyFieldImportDto.Type,
                BackgroundColour = geologyFieldImportDto.BackgroundColour,
                TextColour = geologyFieldImportDto.TextColour,
                IsActive = geologyFieldImportDto.IsActive,
                ReferenceId = referenceId,
                NumberId = numberId,
                RockTypeId = rockTypeId,
                RockGroupId = rockGroupId
            };

            await _geologyFieldService.CreateAsync(geologyField, true);
        }
    }

    private async Task<int?> GetReferenceIdAsync(GeologyFieldImportDto geologyFieldImportDto)
    {
        switch (geologyFieldImportDto.Type)
        {
            case FieldType.Number:
            {
                var number = await _numberRepository.GetAll()
                    .Where(x => x.Name == geologyFieldImportDto.Reference && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return number?.Id;
            }
            case FieldType.RockGroup:
            {
                var rockGroup = await _rockGroupRepository.GetAll()
                    .Where(x => x.Name == geologyFieldImportDto.Reference && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return rockGroup?.Id;
            }
            case FieldType.PickList:
            {
                var pickList = await _pickListRepository.GetAll()
                    .Where(x => x.Name == geologyFieldImportDto.Reference && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return pickList?.Id;
            }
            case FieldType.Description:
            {
                var geologyDescription = await _geologyDescriptionRepository.GetAll()
                    .Where(x => x.Name == geologyFieldImportDto.Reference && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return geologyDescription?.Id;
            }

            case FieldType.Date:
            {
                var geologyDate = await _geologyDateRepository.GetAll()
                    .Where(x => x.Name == geologyFieldImportDto.Reference && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return geologyDate?.Id;
            }
        }

        return null;
    }

    private async Task<int?> GetNumberIdAsync(GeologyFieldImportDto geologyFieldImportDto)
    {
        var parts = geologyFieldImportDto.Reference.Split(" - ", StringSplitOptions.TrimEntries);

        var numberName = parts.Length > 1 ? parts[1] : string.Empty;
        switch (geologyFieldImportDto.Type)
        {
            case FieldType.RockTypeNumber:
            case FieldType.RockSelectNumber:
            {
                var number = await _numberRepository.GetAll()
                    .Where(x => x.Name == numberName && x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();
                return number?.Id;
            }
        }

        return null;
    }

    private async Task<int?> GetRockTypeIdAsync(GeologyFieldImportDto geologyFieldImportDto)
    {
        if (geologyFieldImportDto.Type != FieldType.RockTypeNumber) return null;

        var parts = geologyFieldImportDto.Reference.Split(" - ", StringSplitOptions.TrimEntries);
        var rockTypeName = parts.Length > 1 ? parts[0] : string.Empty;

        var rockType = await _rockTypeRepository.GetAll()
            .Where(x => x.Name == rockTypeName && x.TenantId == _abpSession.GetTenantId())
            .FirstOrDefaultAsync();
        return rockType?.Id;
    }

    private async Task<int?> GetRockGroupIdAsync(GeologyFieldImportDto geologyFieldImportDto)
    {
        if (geologyFieldImportDto.Type != FieldType.RockSelectNumber) return null;

        var parts = geologyFieldImportDto.Reference.Split(" - ", StringSplitOptions.TrimEntries);
        var rockGroupName = parts.Length > 1 ? parts[0] : string.Empty;

        var rockGroup = await _rockGroupRepository.GetAll()
            .Where(x => x.Name == rockGroupName && x.TenantId == _abpSession.GetTenantId())
            .FirstOrDefaultAsync();
        return rockGroup?.Id;
    }

    private static List<GeologySuiteDto> ReadDataGeologySuite(ExcelPackage package)
    {
        var geologySuiteExportsDto = new List<GeologySuiteExportDto>();

        var worksheet = package.Workbook.Worksheets[3];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var geologySuiteExportDto = new GeologySuiteExportDto
            {
                Name = worksheet.Cells[row, 1].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 2].Text),
                GeologyFieldName = worksheet.Cells[row, 3].Text,
                GeologyField = worksheet.Cells[row, 4].Text,
                GeologyFieldSequence = Convert.ToInt32(worksheet.Cells[row, 5].Text),
                GeologyFieldIsActive = bool.Parse(worksheet.Cells[row, 6].Text),
                BackgroundColour = worksheet.Cells[row, 7].Text,
                TextColour = worksheet.Cells[row, 8].Text,
            };

            geologySuiteExportsDto.Add(geologySuiteExportDto);
        }

        var geologySuitesDto = geologySuiteExportsDto
            .GroupBy(item => new { item.Name, item.IsActive, item.BackgroundColour, item.TextColour })
            .Select(group => new GeologySuiteDto()
            {
                Name = group.Key.Name,
                IsActive = group.Key.IsActive,
                GeologySuiteFields = group.Select(g => new GeologySuiteFieldDto
                    {
                        Name = g.GeologyFieldName,
                        Sequence = g.GeologyFieldSequence,
                        IsActive = g.GeologyFieldIsActive,
                        BackgroundColour = g.BackgroundColour,
                        TextColour = g.TextColour,
                        GeologyField = new GeologyFieldDto
                        {
                            Name = g.GeologyField
                        }
                    })
                    .ToList()
            })
            .ToList();

        return geologySuitesDto;
    }

    private async Task ImportDataGeologySuiteAsync(List<GeologySuiteDto> geologySuiteExportsDto)
    {
        foreach (var geologySuiteExportDto in geologySuiteExportsDto)
        {
            var geologySuiteCreate = new CreateGeologySuiteDto
            {
                Name = geologySuiteExportDto.Name,
                IsActive = geologySuiteExportDto.IsActive,
            };
            var geologySuite = await _geologySuiteService.CreateAsync(geologySuiteCreate, true);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            if (geologySuiteExportDto.GeologySuiteFields == null) continue;
            foreach (var geologySuiteFieldDto in geologySuiteExportDto.GeologySuiteFields)
            {
                var geologyField = await _geologyFieldRepository.GetAll()
                    .Where(x => x.Name == geologySuiteFieldDto.GeologyField.Name &&
                                x.TenantId == _abpSession.GetTenantId())
                    .FirstOrDefaultAsync();

                if (geologyField == null) continue;
                var geologySuiteFieldCreate = new CreateGeologySuiteFieldDto()
                {
                    GeologySuiteId = geologySuite.Id,
                    Sequence = geologySuiteFieldDto.Sequence,
                    Name = geologySuiteFieldDto.Name,
                    GeologyFieldId = geologyField.Id,
                    IsActive = geologySuiteFieldDto.IsActive,
                    BackgroundColour = geologySuiteFieldDto.BackgroundColour,
                    TextColour = geologySuiteFieldDto.TextColour,
                };
                await _geologySuiteFieldService.CreateAsync(geologySuiteFieldCreate, true);
            }
        }
    }

    private static List<WorkRoleDto> ReadDataWorkRole(ExcelPackage package)
    {
        var workRoleExportsDto = new List<WorkRoleExportDto>();

        var worksheet = package.Workbook.Worksheets[4];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var workRoleExportDto = new WorkRoleExportDto
            {
                Name = worksheet.Cells[row, 1].Text,
                Description = worksheet.Cells[row, 2].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 3].Text),
                GeologySuite = worksheet.Cells[row, 4].Text,
            };

            workRoleExportsDto.Add(workRoleExportDto);
        }

        var workRoles = workRoleExportsDto
            .GroupBy(item => new { item.Name, item.Description, item.IsActive })
            .Select(group => new WorkRoleDto()
            {
                Name = group.Key.Name,
                Description = group.Key.Description,
                IsActive = group.Key.IsActive,
                GeologySuites = group.Select(g => new GeologySuiteDto() { Name = g.GeologySuite }).ToList()
            })
            .ToList();

        return workRoles;
    }

    private async Task ImportDataWorkRoleAsync(List<WorkRoleDto> workRoles)
    {
        foreach (var workRoleDto in workRoles)
        {
            var geologySuiteNames = workRoleDto.GeologySuites.Select(x => x.Name).ToList();
            var geologySuites = await _geologySuiteRepository.GetAll()
                .Where(x => geologySuiteNames.Contains(x.Name) && x.TenantId == _abpSession.GetTenantId())
                .Select(x => x.Id)
                .ToListAsync();

            var workRoleCreate = new CreateWorkRoleDto()
            {
                Name = workRoleDto.Name,
                Description = workRoleDto.Description,
                IsActive = workRoleDto.IsActive,
                GeologySuiteIds = geologySuites
            };

            await _workRoleService.CreateAsync(workRoleCreate, true);
        }
    }

    private static List<ColourDto> ReadDataColour(ExcelPackage package)
    {
        var colours = new List<ColourDto>();

        var worksheet = package.Workbook.Worksheets[5];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new ColourDto
            {
                Name = worksheet.Cells[row, 1].Text,
                HexCode = worksheet.Cells[row, 2].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 3].Text),
            };

            colours.Add(rockType);
        }

        return colours;
    }

    private async Task ImportDataColourAsync(List<ColourDto> colours)
    {
        foreach (var createColourDto in colours.Select(colour => new CreateColourDto()
                 {
                     Name = colour.Name,
                     HexCode = colour.HexCode,
                     IsActive = colour.IsActive,
                 }))
        {
            await _colourService.CreateAsync(createColourDto, true);
        }
    }

    private static List<UnitDto> ReadDataUnit(ExcelPackage package)
    {
        var units = new List<UnitDto>();

        var worksheet = package.Workbook.Worksheets[7];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new UnitDto
            {
                Name = worksheet.Cells[row, 1].Text,
                Code = worksheet.Cells[row, 2].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 3].Text),
            };

            units.Add(rockType);
        }

        return units;
    }

    private async Task ImportDataUnitAsync(List<UnitDto> units)
    {
        foreach (var createUnitDto in units.Select(colour => new CreateUnitDto()
                 {
                     Name = colour.Name,
                     Code = colour.Code,
                     IsActive = colour.IsActive,
                 }))
        {
            await _unitService.CreateAsync(createUnitDto, true);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    private static List<NumberImportDto> ReadDataNumber(ExcelPackage package)
    {
        var numbers = new List<NumberImportDto>();

        var worksheet = package.Workbook.Worksheets[6];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var valueTypeString = worksheet.Cells[row, 2].Text;
            if (!Enum.TryParse(valueTypeString, out ValueType valueType))
            {
                valueType = ValueType.Integer;
            }

            var rockType = new NumberImportDto
            {
                Name = worksheet.Cells[row, 1].Text,
                ValueType = valueType,
                UpperLimit = Convert.ToDouble(worksheet.Cells[row, 3].Text),
                LowerLimit = Convert.ToDouble(worksheet.Cells[row, 4].Text),
                IsPercent = bool.Parse(worksheet.Cells[row, 5].Text),
                IsActive = bool.Parse(worksheet.Cells[row, 6].Text),
                UnitName = worksheet.Cells[row, 7].Text,
            };

            numbers.Add(rockType);
        }

        return numbers;
    }

    private async Task ImportDataNumberAsync(List<NumberImportDto> numbers)
    {
        foreach (var numberDto in numbers)
        {
            var unit = await _unitRepository.GetAll()
                .AsNoTracking()
                .Where(x => x.Name == numberDto.UnitName && x.TenantId == _abpSession.GetTenantId())
                .FirstOrDefaultAsync();

            if (unit == null) continue;
            var createNumberDto = new CreateNumberDto()
            {
                Name = numberDto.Name,
                ValueType = numberDto.ValueType,
                UpperLimit = numberDto.UpperLimit,
                LowerLimit = numberDto.LowerLimit,
                IsPercent = numberDto.IsPercent,
                IsActive = numberDto.IsActive,
                UnitId = unit.Id,
            };

            await _numberService.CreateAsync(createNumberDto, true);
        }

        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    private static List<GeologyDateDto> ReadDataGeologyDate(ExcelPackage package)
    {
        var geologyDates = new List<GeologyDateDto>();

        var worksheet = package.Workbook.Worksheets[8];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new GeologyDateDto
            {
                Name = worksheet.Cells[row, 1].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 2].Text),
            };

            geologyDates.Add(rockType);
        }

        return geologyDates;
    }

    private async Task ImportDataGeologyDateAsync(List<GeologyDateDto> geologyDates)
    {
        foreach (var createGeologyDateDto in geologyDates.Select(colour => new CreateGeologyDateDto()
                 {
                     Name = colour.Name,
                     IsActive = colour.IsActive,
                 }))
        {
            await _geologyDateService.CreateAsync(createGeologyDateDto, true);
        }
    }

    private static List<GeologyDescriptionDto> ReadDataGeologyDescription(ExcelPackage package)
    {
        var geologyDescriptions = new List<GeologyDescriptionDto>();

        var worksheet = package.Workbook.Worksheets[9];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var geologyDescription = new GeologyDescriptionDto
            {
                Name = worksheet.Cells[row, 1].Text,
                FieldHeight = Convert.ToInt32(worksheet.Cells[row, 2].Text),
                IsActive = bool.Parse(worksheet.Cells[row, 3].Text),
            };

            geologyDescriptions.Add(geologyDescription);
        }

        return geologyDescriptions;
    }

    private async Task ImportDataGeologyDescriptionAsync(List<GeologyDescriptionDto> units)
    {
        foreach (var createGeologyDescriptionDto in units.Select(colour => new CreateGeologyDescriptionDto()
                 {
                     Name = colour.Name,
                     FieldHeight = colour.FieldHeight,
                     IsActive = colour.IsActive,
                 }))
        {
            await _geologyDescriptionService.CreateAsync(createGeologyDescriptionDto, true);
        }
    }

    private static List<PickListDto> ReadDataPickList(ExcelPackage package)
    {
        var pickListExports = new List<PickListExportDto>();

        var worksheet = package.Workbook.Worksheets[10];
        var rowCount = worksheet.Dimension.Rows;

        for (var row = 2; row <= rowCount; row++)
        {
            var rockType = new PickListExportDto()
            {
                Name = worksheet.Cells[row, 1].Text,
                IsActive = bool.Parse(worksheet.Cells[row, 2].Text),
                ItemName = worksheet.Cells[row, 3].Text,
                ItemSequnce = Convert.ToInt32(worksheet.Cells[row, 4].Text),
                ItemIsActive = bool.Parse(worksheet.Cells[row, 5].Text),
            };

            pickListExports.Add(rockType);
        }

        var pickLists = pickListExports
            .GroupBy(item => new { item.Name, item.IsActive })
            .Select(group => new PickListDto()
            {
                Name = group.Key.Name,
                IsActive = group.Key.IsActive,
                PickListItems = group.Select(g => new PickListItemDto()
                    { Name = g.ItemName, Sequence = g.ItemSequnce, IsActive = g.ItemIsActive }).ToList()
            })
            .ToList();

        return pickLists;
    }

    private async Task ImportDataPickListAsync(List<PickListDto> pickLists)
    {
        foreach (var createPickListDto in pickLists.Select(geologySuite => new CreatePickListDto()
                 {
                     Name = geologySuite.Name,
                     IsActive = geologySuite.IsActive,
                     PickListitems = geologySuite.PickListItems.Select(x => new CreatePickListItemDto()
                     {
                         Name = x.Name,
                         IsActive = x.IsActive,
                         Sequence = x.Sequence,
                     }).ToList()
                 }))
        {
            await _pickListService.CreateAsync(createPickListDto, true);
        }
    }

    private async Task<Setting> ValidateSettingEntity(int id)
    {
        var setting = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (setting == null)
        {
            throw new EntityNotFoundException(typeof(Setting), id);
        }

        return setting;
    }

    private static string GetReferenceName(GeologyField geologyField)
    {
        return geologyField.Type switch
        {
            FieldType.Colour => string.Empty,
            FieldType.Number => geologyField.Number.Name ?? string.Empty,
            FieldType.RockGroup => geologyField.RockGroup.Name ?? string.Empty,
            FieldType.PickList => geologyField.PickList.Name ?? string.Empty,
            FieldType.Description => geologyField.GeologyDescription?.Name ?? string.Empty,
            FieldType.RockTypeNumber =>
                $"{geologyField.RockTypeNumber?.RockType.Name} - {geologyField.RockTypeNumber?.Number.Name}",
            FieldType.RockSelectNumber =>
                $"{geologyField.RockSelectNumber?.RockGroup.Name} - {geologyField.RockSelectNumber?.Number.Name}",
            FieldType.Date => geologyField.GeologyDate?.Name ?? string.Empty,
            FieldType.RockNode => geologyField.RockNode?.Name ?? string.Empty,
            FieldType.CheckBox => geologyField.GeologyCheckBox?.Name ?? string.Empty,
            FieldType.Longitude => geologyField.GeologyLongitude?.Name ?? string.Empty,
            FieldType.Latitude => geologyField.GeologyLatitude?.Name ?? string.Empty,
            FieldType.Url => geologyField.GeologyUrl?.Name ?? string.Empty,
            _ => throw new UserFriendlyException("Invalid field type.")
        };
    }
}