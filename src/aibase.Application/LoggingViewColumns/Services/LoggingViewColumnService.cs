using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.LoggingViewColumns.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.LoggingViewColumns.Services;

/// <inheritdoc />
public class LoggingViewColumnService : ILoggingViewColumnService
{
    private readonly IRepository<LoggingViewColumn, int> _repository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;


    /// <summary>
    /// 
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="unitOfWorkManager"></param>
    /// <param name="abpSession"></param>
    /// <param name="mapper"></param>
    public LoggingViewColumnService(
        IRepository<LoggingViewColumn, int> repository,
        IUnitOfWorkManager unitOfWorkManager,
        IAbpSession abpSession,
        IMapper mapper)
    {
        _repository = repository;
        _unitOfWorkManager = unitOfWorkManager;
        _abpSession = abpSession;
        _mapper = mapper;
    }

    /// <inheritdoc />
    public async Task<LoggingViewColumn> CreateAsync(CreateLoggingViewColumnDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingLoggingViewColumn =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name && d.LoggingViewId == input.LoggingViewId);
        if (existingLoggingViewColumn != null)
        {
            if (returnExist)
            {
                return existingLoggingViewColumn;
            }

            throw new UserFriendlyException(
                $"The LoggingView with the name {existingLoggingViewColumn.Name} already exists.");
        }

        var loggingViewColumn = new LoggingViewColumn
        {
            Name = input.Name,
            ColumnClass = input.ColumnClass,
            Width = input.Width,
            LoggingViewId = input.LoggingViewId,
            NumberRangeId = input.NumberRangeId,
            TenantId = tenantId,
            Sequence = input.Sequence ?? 0,
            WidthFactor = input.WidthFactor,
            Type = input.Type,
            StandardType = input.StandardType,
            ImageTypeId = input.ImageTypeId,
            ImageSubtypeId = input.ImageSubTypeId
        };

        switch (input.ColumnClass)
        {
            case ColumnClass.Geology:
            {
                loggingViewColumn.GeologySuiteId = input.SuiteId;
                loggingViewColumn.GeologySuiteFieldId = input.FieldId;
                break;
            }
            case ColumnClass.Assay:
            {
                loggingViewColumn.AssaySuiteId = input.SuiteId;
                loggingViewColumn.AssayAttributeId = input.FieldId;
                break;
            }
            case ColumnClass.Geophysics:
                loggingViewColumn.SuiteId = input.SuiteId;
                loggingViewColumn.AttributeId = input.FieldId;
                break;
            case ColumnClass.Structure:
                break;
        }

        await _repository.InsertAsync(loggingViewColumn);
        await _unitOfWorkManager.Current.SaveChangesAsync();

        return loggingViewColumn;
    }

    /// <inheritdoc />
    public async Task<LoggingViewColumnDto> UpdateAsync(UpdateLoggingViewColumnDto input)
    {
        var loggingViewColumn = await LoggingViewColumnEntity(input.Id);

        switch (input.ColumnClass)
        {
            case ColumnClass.Geology:
            {
                loggingViewColumn.GeologySuiteId = input.SuiteId ?? loggingViewColumn.GeologySuiteId;
                loggingViewColumn.GeologySuiteFieldId = input.FieldId ?? loggingViewColumn.GeologySuiteFieldId;
                break;
            }
            case ColumnClass.Assay:
            {
                loggingViewColumn.AssaySuiteId = input.SuiteId ?? loggingViewColumn.AssaySuiteId;
                loggingViewColumn.AssayAttributeId = input.FieldId ?? loggingViewColumn.AssayAttributeId;
                break;
            }
            case ColumnClass.Geophysics:
                loggingViewColumn.SuiteId = input.SuiteId ?? loggingViewColumn.SuiteId;
                loggingViewColumn.AttributeId = input.FieldId ?? loggingViewColumn.AttributeId;
                break;
            case ColumnClass.Structure:
                break;
        }

        loggingViewColumn.Name = input.Name ?? loggingViewColumn.Name;
        loggingViewColumn.ColumnClass = input.ColumnClass ?? loggingViewColumn.ColumnClass;
        loggingViewColumn.Width = input.Width ?? loggingViewColumn.Width;
        loggingViewColumn.LoggingViewId = input.LoggingViewId ?? loggingViewColumn.LoggingViewId;
        loggingViewColumn.NumberRangeId = input.NumberRangeId ?? loggingViewColumn.NumberRangeId;
        loggingViewColumn.Sequence = input.Sequence ?? loggingViewColumn.Sequence;
        loggingViewColumn.WidthFactor = input.WidthFactor ?? loggingViewColumn.WidthFactor;
        loggingViewColumn.Type = input.Type ?? loggingViewColumn.Type;
        loggingViewColumn.StandardType = input.StandardType ?? loggingViewColumn.StandardType;
        loggingViewColumn.ImageTypeId = input.ImageTypeId;
        loggingViewColumn.ImageSubtypeId = input.ImageSubtypeId;

        return await GetAsync(input);
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<LoggingViewColumnDto>> GetAllAsync(PagedLoggingViewColumnResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.GeologySuite, 
                x => x.GeologySuiteField, 
                x => x.AssaySuite,
                x => x.AssayAttribute, 
                x => x.Suite,
                x => x.Attribute,
                x => x.ImageType,
                x => x.ImageSubtype)
            .AsNoTracking()
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .Include(x => x.NumberRange)
            .ThenInclude(x => x.Intervals)
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(), x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .WhereIf(input.LoggingViewId.HasValue, x => x.LoggingViewId == input.LoggingViewId)
            .OrderBy(r => r.Sequence);

        var totalCount = await query.CountAsync();

        var loggingViewColumns = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        return new PagedResultDto<LoggingViewColumnDto>(totalCount,
            _mapper.Map<List<LoggingViewColumnDto>>(loggingViewColumns));
    }

    /// <inheritdoc />
    public async Task<LoggingViewColumnDto> GetAsync(EntityDto<int> input)
    {
        var loggingViewColumn = await LoggingViewColumnEntity(input.Id);
        return _mapper.Map<LoggingViewColumnDto>(loggingViewColumn);
    }

    private async Task<LoggingViewColumn> LoggingViewColumnEntity(int id)
    {
        var loggingViewColumn = await _repository.GetAllIncluding(x => x.GeologySuite, 
            x => x.GeologySuiteField, 
            x => x.AssaySuite,
            x => x.AssayAttribute, 
            x => x.Suite,
            x => x.Attribute,
            x => x.NumberRange,
            x => x.ImageType,
            x => x.ImageSubtype)
            .Include(x => x.GeologySuiteField)
            .ThenInclude(x => x.GeologyField)
            .FirstOrDefaultAsync(x => x.Id == id);

        if (loggingViewColumn == null)
        {
            throw new EntityNotFoundException(typeof(LoggingViewColumn), id);
        }

        return loggingViewColumn;
    }
}