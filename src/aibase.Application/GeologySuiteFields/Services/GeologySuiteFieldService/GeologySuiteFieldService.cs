using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Extensions;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.GeologyCheckBoxs;
using aibase.GeologyCheckBoxs.Dto;
using aibase.GeologyDates;
using aibase.GeologyDates.Dto;
using aibase.GeologyDescriptions;
using aibase.GeologyDescriptions.Dto;
using aibase.GeologyFields;
using aibase.GeologyLatitudes;
using aibase.GeologyLatitudes.Dto;
using aibase.GeologyLongitudes;
using aibase.GeologyLongitudes.Dto;
using aibase.GeologySuiteFields.Dto;
using aibase.GeologyUrls;
using aibase.GeologyUrls.Dto;
using aibase.LoggingViewColumns;
using aibase.Numbers;
using aibase.Numbers.Dto;
using aibase.PickLists;
using aibase.PickLists.Dto;
using aibase.RockGroups;
using aibase.RockGroups.Dto;
using aibase.RockSelectNumbers;
using aibase.RockSelectNumbers.Dto;
using aibase.RockTree;
using aibase.RockTree.Dto;
using aibase.RockTypeNumbers;
using aibase.RockTypeNumbers.Dto;
using aibase.RockTypes;
using aibase.RockTypes.Dto;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace aibase.GeologySuiteFields.Services.GeologySuiteFieldService;

/// <inheritdoc />
public class GeologySuiteFieldService : IGeologySuiteFieldService
{
    private readonly IRepository<GeologySuiteField, int> _repository;
    private readonly IRepository<Number, int> _numberRepository;
    private readonly IRepository<RockGroup, int> _rockGroupRepository;
    private readonly IRepository<PickList, int> _pickListRepository;
    private readonly IRepository<GeologyDescription, int> _geologyDescriptionRepository;
    private readonly IRepository<RockTypeNumber, int> _rockTypeNumberRepository;
    private readonly IRepository<RockSelectNumber, int> _rockSelectNumberRepository;
    private readonly IRepository<GeologyDate, int> _geologyDateRepository;
    private readonly IRepository<RockType, int> _rockTypeRepository;
    private readonly IRepository<RockNode, int> _rockNodeRepository;
    private readonly IRepository<LoggingViewColumn, int> _loggingViewColumnRepository;
    private readonly IRepository<RockNodeRelation, int> _rockNodeRelationRepository;
    private readonly IRepository<GeologyCheckBox, int> _geologyCheckBoxRepository;
    private readonly IRepository<GeologyLatitude, int> _geologyLatitudeRepository;
    private readonly IRepository<GeologyLongitude, int> _geologyLongitudeRepository;
    private readonly IRepository<GeologyUrl, int> _geologyUrlRepository;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public GeologySuiteFieldService(
        IRepository<GeologySuiteField, int> repository,
        IRepository<Number, int> numberRepository,
        IRepository<RockGroup, int> rockGroupRepository,
        IRepository<PickList, int> pickListRepository,
        IRepository<GeologyDescription, int> geologyDescriptionRepository,
        IRepository<RockTypeNumber, int> rockTypeNumberRepository,
        IRepository<RockSelectNumber, int> rockSelectNumberRepository,
        IRepository<GeologyDate, int> geologyDateRepository,
        IRepository<RockType, int> rockTypeRepository,
        IAbpSession abpSession,
        IMapper mapper,
        IRepository<RockNode, int> rockNodeRepository,
        IRepository<LoggingViewColumn, int> loggingViewColumnRepository,
        IRepository<RockNodeRelation, int> rockNodeRelationRepository,
        IRepository<GeologyCheckBox, int> geologyCheckBoxRepository,
        IRepository<GeologyLatitude, int> geologyLatitudeRepository,
        IRepository<GeologyLongitude, int> geologyLongitudeRepository,
        IRepository<GeologyUrl, int> geologyUrlRepository)
    {
        _repository = repository;
        _numberRepository = numberRepository;
        _rockGroupRepository = rockGroupRepository;
        _pickListRepository = pickListRepository;
        _geologyDescriptionRepository = geologyDescriptionRepository;
        _rockTypeNumberRepository = rockTypeNumberRepository;
        _rockSelectNumberRepository = rockSelectNumberRepository;
        _geologyDateRepository = geologyDateRepository;
        _rockTypeRepository = rockTypeRepository;
        _abpSession = abpSession;
        _mapper = mapper;
        _rockNodeRepository = rockNodeRepository;
        _loggingViewColumnRepository = loggingViewColumnRepository;
        _rockNodeRelationRepository = rockNodeRelationRepository;
        _geologyCheckBoxRepository = geologyCheckBoxRepository;
        _geologyLatitudeRepository = geologyLatitudeRepository;
        _geologyLongitudeRepository = geologyLongitudeRepository;
        _geologyUrlRepository = geologyUrlRepository;
    }

    /// <inheritdoc />
    public async Task<GeologySuiteField> CreateAsync(CreateGeologySuiteFieldDto input, bool returnExist = false)
    {
        if (_abpSession.TenantId == null)
        {
            throw new UserFriendlyException("The account does not have permission to perform this feature.");
        }

        var tenantId = _abpSession.GetTenantId();

        var existingGeologySuiteField =
            await _repository.FirstOrDefaultAsync(d => d.TenantId == tenantId && d.Name == input.Name);
        if (existingGeologySuiteField != null)
        {
            if (returnExist)
            {
                return existingGeologySuiteField;
            }

            throw new UserFriendlyException(
                $"The Geology Suite Field with the name {existingGeologySuiteField.Name} already exists.");
        }

        var geologySuiteField = new GeologySuiteField()
        {
            GeologySuiteId = input.GeologySuiteId,
            Sequence = input.Sequence,
            Name = input.Name,
            GeologyFieldId = input.GeologyFieldId,
            IsActive = input.IsActive,
            IsMandatory = input.IsMandatory,
            BackgroundColour = input.BackgroundColour,
            TextColour = input.TextColour,
            TenantId = tenantId,
        };

        await _repository.InsertAsync(geologySuiteField);
        return geologySuiteField;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<GeologySuiteFieldDto>> GetAllAsync(PagedGeologySuiteFieldResultRequestDto input)
    {
        var query = _repository.GetAllIncluding(x => x.GeologyField)
            .AsNoTracking()
            .WhereIf(_abpSession.TenantId.HasValue, x => x.TenantId == _abpSession.GetTenantId())
            .WhereIf(input.IsActive.HasValue, x => x.IsActive == input.IsActive)
            .WhereIf(!input.Keyword.IsNullOrWhiteSpace(),
                x => input.Keyword != null && x.Name.ToLower().Contains(input.Keyword.ToLower()))
            .OrderBy(r => r.Name);

        var totalCount = await query.CountAsync();

        var geologySuiteFields = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        var geologySuiteFieldDto = _mapper.Map<List<GeologySuiteFieldDto>>(geologySuiteFields);

        await PopulateRelatedFieldsAsync(geologySuiteFieldDto);

        return new PagedResultDto<GeologySuiteFieldDto>(totalCount, geologySuiteFieldDto);
    }

    /// <inheritdoc />
    public async Task<GeologySuiteFieldDto> GetAsync(EntityDto<int> input)
    {
        var geologySuiteField = await ValidateGeologySuiteFieldEntity(input.Id);
        return _mapper.Map<GeologySuiteFieldDto>(geologySuiteField);
    }

    /// <inheritdoc />
    public async Task<GeologySuiteFieldDto> UpdateAsync(UpdateGeologySuiteFieldDto input)
    {
        var geologySuiteField = await ValidateGeologySuiteFieldEntity(input.Id);

        geologySuiteField.GeologySuiteId = input.GeologySuiteId ?? geologySuiteField.GeologySuiteId;
        geologySuiteField.Sequence = input.Sequence ?? geologySuiteField.Sequence;
        geologySuiteField.Name = input.Name ?? geologySuiteField.Name;
        geologySuiteField.GeologyFieldId = input.GeologyFieldId ?? geologySuiteField.GeologyFieldId;
        geologySuiteField.IsActive = input.IsActive ?? geologySuiteField.IsActive;
        geologySuiteField.IsMandatory = input.IsMandatory ?? geologySuiteField.IsMandatory;
        geologySuiteField.BackgroundColour = input.BackgroundColour ?? geologySuiteField.BackgroundColour;
        geologySuiteField.TextColour = input.TextColour ?? geologySuiteField.TextColour;

        return await GetAsync(input);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="geologySuiteFields"></param>
    public async Task PopulateRelatedFieldsAsync(List<GeologySuiteFieldDto> geologySuiteFields)
    {
        var numberIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Number)
            .Select(f => f.GeologyField.NumberId)
            .Distinct().ToList();
        var rockGroupIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.RockGroup)
            .Select(f => f.GeologyField.RockGroupId)
            .Distinct().ToList();
        var pickListIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.PickList)
            .Select(f => f.GeologyField.PickListId)
            .Distinct().ToList();
        var descriptionIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Description)
            .Select(f => f.GeologyField.GeologyDescriptionId)
            .Distinct().ToList();
        var rockTypeNumberIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.RockTypeNumber)
            .Select(f => f.GeologyField.RockTypeNumberId)
            .Distinct().ToList();
        var rockSelectNumberIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.RockSelectNumber)
            .Select(f => f.GeologyField.RockSelectNumberId)
            .Distinct().ToList();
        var dateIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Date)
            .Select(f => f.GeologyField.GeologyDateId)
            .Distinct().ToList();
        var checkboxIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.CheckBox)
            .Select(f => f.GeologyField.GeologyCheckBoxId)
            .Distinct().ToList();
        var latitudeIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Latitude)
            .Select(f => f.GeologyField.GeologyLatitudeId)
            .Distinct().ToList();
        var longitudeIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Longitude)
            .Select(f => f.GeologyField.GeologyLongitudeId)
            .Distinct().ToList();
        var urlIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.Url)
            .Select(f => f.GeologyField.GeologyUrlId)
            .Distinct().ToList();
        var rockNodeIds = geologySuiteFields.Where(f => f.GeologyField.Type == FieldType.RockNode)
            .Select(f => f.GeologyField.RockNodeId)
            .Distinct().ToList();

        var numbers = await _numberRepository.GetAllIncluding(x => x.Unit)
            .AsNoTracking()
            .Where(v => numberIds.Contains(v.Id))
            .ToDictionaryAsync(v => v.Id);
        var rockGroups = await _rockGroupRepository.GetAll()
            .AsNoTracking()
            .Where(rg => rockGroupIds.Contains(rg.Id))
            .ToDictionaryAsync(rg => rg.Id);
        var pickLists = await _pickListRepository.GetAllIncluding(x => x.PickListItems)
            .AsNoTracking()
            .Where(pl => pickListIds.Contains(pl.Id))
            .ToDictionaryAsync(pl => pl.Id);
        var descriptions = await _geologyDescriptionRepository.GetAll()
            .AsNoTracking()
            .Where(d => descriptionIds.Contains(d.Id))
            .ToDictionaryAsync(d => d.Id);
        var rockTypeNumbers = await _rockTypeNumberRepository.GetAllIncluding(x => x.Number, x => x.RockType)
            .AsNoTracking()
            .Include(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(r => rockTypeNumberIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var rockSelectNumbers = await _rockSelectNumberRepository.GetAllIncluding(x => x.Number, x => x.RockGroup)
            .AsNoTracking()
            .Include(x => x.Number)
            .ThenInclude(x => x.Unit)
            .Where(r => rockSelectNumberIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var dates = await _geologyDateRepository.GetAll()
            .AsNoTracking()
            .Where(r => dateIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var checkboxs = await _geologyCheckBoxRepository.GetAll()
            .AsNoTracking()
            .Where(r => checkboxIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var latitudes = await _geologyLatitudeRepository.GetAll()
            .AsNoTracking()
            .Where(r => latitudeIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var longitudes = await _geologyLongitudeRepository.GetAll()
            .AsNoTracking()
            .Where(r => longitudeIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var urls = await _geologyUrlRepository.GetAll()
            .AsNoTracking()
            .Where(r => urlIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);
        var rockNodes = await _rockNodeRepository.GetAll()
            .AsNoTracking()
            .Where(r => rockNodeIds.Contains(r.Id))
            .ToDictionaryAsync(r => r.Id);

        foreach (var field in geologySuiteFields)
        {
            switch (field.GeologyField.Type)
            {
                case FieldType.Colour:
                    break;

                case FieldType.Number:
                    if (field.GeologyField.NumberId.HasValue &&
                        numbers.TryGetValue((int)field.GeologyField.NumberId, out var value))
                    {
                        field.GeologyField.Number = _mapper.Map<NumberDto>(value);
                    }

                    break;

                case FieldType.RockGroup:
                    if (field.GeologyField.RockGroupId.HasValue &&
                        rockGroups.TryGetValue((int)field.GeologyField.RockGroupId, out var rockGroup))
                    {
                        var rockGroupDto = _mapper.Map<RockGroupDto>(rockGroup);

                        var group = rockGroup;

                        rockGroupDto.RockTypes = await _rockTypeRepository.GetAll()
                            .AsNoTracking()
                            .Where(x => x.RockGroupRockTypes.Any(y => y.RockGroupId == group.Id))
                            .Select(b => new RockTypeDto()
                            {
                                Id = b.Id,
                                Name = b.Name,
                                Code = b.Code,
                                Description = b.Description,
                                IsActive = b.IsActive,
                                RockStyleId = b.RockStyleId,
                            })
                            .OrderBy(x => x.Name)
                            .ToListAsync();

                        field.GeologyField.RockGroup = rockGroupDto;
                    }

                    break;

                case FieldType.PickList:
                    if (field.GeologyField.PickListId.HasValue &&
                        pickLists.TryGetValue((int)field.GeologyField.PickListId, out var pickList))
                    {
                        field.GeologyField.PickList = _mapper.Map<PickListDto>(pickList);
                    }

                    break;

                case FieldType.Description:
                    if (field.GeologyField.GeologyDescriptionId.HasValue &&
                        descriptions.TryGetValue((int)field.GeologyField.GeologyDescriptionId, out var description))
                    {
                        field.GeologyField.GeologyDescription = _mapper.Map<GeologyDescriptionDto>(description);
                    }

                    break;

                case FieldType.RockTypeNumber:
                    if (field.GeologyField.RockTypeNumberId.HasValue &&
                        rockTypeNumbers.TryGetValue((int)field.GeologyField.RockTypeNumberId, out var rockTypeNumber))
                    {
                        field.GeologyField.RockTypeNumber = _mapper.Map<RockTypeNumberDto>(rockTypeNumber);
                    }

                    break;

                case FieldType.RockSelectNumber:
                    if (field.GeologyField.RockSelectNumberId.HasValue && rockSelectNumbers.TryGetValue(
                            (int)field.GeologyField.RockSelectNumberId,
                            out var rockSelectNumber))
                    {
                        var rockSelectNumberDto = _mapper.Map<RockSelectNumberDto>(rockSelectNumber);

                        var rockNumber = rockSelectNumber;

                        rockSelectNumberDto.RockGroup.RockTypes = await _rockTypeRepository.GetAll()
                            .AsNoTracking()
                            .Where(x => x.RockGroupRockTypes.Any(y =>
                                y.RockGroupId == rockNumber.RockGroupId))
                            .Select(b => new RockTypeDto()
                            {
                                Id = b.Id,
                                Name = b.Name,
                                Code = b.Code,
                                Description = b.Description,
                                IsActive = b.IsActive,
                                RockStyleId = b.RockStyleId,
                            })
                            .OrderBy(x => x.Name)
                            .ToListAsync();

                        field.GeologyField.RockSelectNumber = rockSelectNumberDto;
                    }

                    break;

                case FieldType.Date:
                    if (field.GeologyField.GeologyDateId.HasValue &&
                        dates.TryGetValue((int)field.GeologyField.GeologyDateId, out var date))
                    {
                        field.GeologyField.GeologyDate = _mapper.Map<GeologyDateDto>(date);
                    }

                    break;

                case FieldType.CheckBox:
                    if (field.GeologyField.GeologyCheckBoxId.HasValue &&
                        checkboxs.TryGetValue((int)field.GeologyField.GeologyCheckBoxId, out var checkBox))
                    {
                        field.GeologyField.GeologyCheckBox = _mapper.Map<GeologyCheckBoxDto>(checkBox);
                    }

                    break;

                case FieldType.Latitude:
                    if (field.GeologyField.GeologyLatitudeId.HasValue &&
                        latitudes.TryGetValue((int)field.GeologyField.GeologyLatitudeId, out var geologyLatitude))
                    {
                        field.GeologyField.GeologyLatitude = _mapper.Map<GeologyLatitudeDto>(geologyLatitude);
                    }

                    break;

                case FieldType.Longitude:
                    if (field.GeologyField.GeologyLongitudeId.HasValue &&
                        longitudes.TryGetValue((int)field.GeologyField.GeologyLongitudeId, out var geologyLongitude))
                    {
                        field.GeologyField.GeologyLongitude = _mapper.Map<GeologyLongitudeDto>(geologyLongitude);
                    }

                    break;

                case FieldType.Url:
                    if (field.GeologyField.GeologyUrlId.HasValue &&
                        urls.TryGetValue((int)field.GeologyField.GeologyUrlId, out var geologyUrl))
                    {
                        field.GeologyField.GeologyUrl = _mapper.Map<GeologyUrlDto>(geologyUrl);
                    }

                    break;

                case FieldType.RockNode:
                    if (field.GeologyField.RockNodeId.HasValue &&
                        rockNodes.TryGetValue((int)field.GeologyField.RockNodeId, out var rockNode))
                    {
                        var rockNodeDto = _mapper.Map<RockNodeDto>(rockNode);
                        field.GeologyField.RockNode = rockNodeDto;

                        var treeNode = new TreeNodeDto
                        {
                            Id = rockNodeDto.Id,
                            Name = rockNodeDto.Name,
                            Code = rockNodeDto.Code,
                            Description = rockNodeDto.Description,
                            NodeType = rockNodeDto.NodeType,
                            RockTypeId = rockNodeDto.RockTypeId,
                            IsActive = rockNodeDto.IsActive,
                            DisplayColor = rockNodeDto.DisplayColor,
                            IconUrl = rockNodeDto.IconUrl,
                            ParentId = null // Root nodes have no parent
                        };

                        await BuildTreeRecursivelyAsync(treeNode, _abpSession.GetTenantId(), []);

                        field.GeologyField.TreeNode = treeNode;
                    }

                    break;
            }
        }
    }

    /// <inheritdoc />
    public async Task DeleteAsync(EntityDto<int> input)
    {
        var geologySuiteField = await ValidateGeologySuiteFieldEntity(input.Id);

        // Check if the GeologySuiteField is referenced in LoggingViewColumns
        var hasReferences = await _loggingViewColumnRepository.GetAll()
            .AnyAsync(x => x.GeologySuiteFieldId == input.Id);

        if (hasReferences)
        {
            throw new UserFriendlyException(
                $"Cannot delete the Geology Suite Field '{geologySuiteField.Name}' because it is being used in one or more Logging View Columns.");
        }

        await _repository.DeleteAsync(geologySuiteField);
    }

    private async Task<GeologySuiteField> ValidateGeologySuiteFieldEntity(int id)
    {
        var geologySuiteField = await _repository.FirstOrDefaultAsync(x => x.Id == id);

        if (geologySuiteField == null)
        {
            throw new EntityNotFoundException(typeof(GeologySuiteField), id);
        }

        return geologySuiteField;
    }

    private async Task BuildTreeRecursivelyAsync(TreeNodeDto parentNode, int tenantId, HashSet<int> visitedNodes)
    {
        // Add current node to visited set to prevent cycles
        if (!visitedNodes.Add(parentNode.Id))
        {
            return;
        }

        // Get child nodes for this parent from the relations
        var childRelations = await _rockNodeRelationRepository.GetAllIncluding(r => r.ChildNode)
            .Where(r => r.ParentNodeId == parentNode.Id &&
                        r.ChildNode.TenantId == tenantId)
            .OrderBy(r => r.DisplayOrder)
            .ToListAsync();

        foreach (var relation in childRelations)
        {
            // Skip if we've already visited this node (prevents cycles)
            if (visitedNodes.Contains(relation.ChildNodeId)) continue;

            // Get the full child node entity
            var childNode = await _rockNodeRepository.GetAsync(relation.ChildNodeId);
            if (childNode.TenantId != tenantId) continue; // Additional tenant safety check

            var childTreeNode = new TreeNodeDto
            {
                Id = childNode.Id,
                Name = childNode.Name,
                Code = childNode.Code,
                Description = childNode.Description,
                NodeType = childNode.NodeType,
                RockTypeId = childNode.RockTypeId,
                IsActive = childNode.IsActive,
                DisplayColor = childNode.DisplayColor,
                IconUrl = childNode.IconUrl,
                ParentId = parentNode.Id
            };

            // Recursively add children to this child node
            await BuildTreeRecursivelyAsync(childTreeNode, tenantId, new HashSet<int>(visitedNodes));

            // Add child to parent's children list
            parentNode.Children.Add(childTreeNode);
        }
    }
}