using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using aibase.Configuration;
using aibase.FileEntity;
using aibase.ImageCrops;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.CropService;
using aibase.Images.Services.ProcessService.Handler;
using aibase.Images.Services.UploadService.Handler;
using aibase.Models.Dto;
using aibase.RockLines.Dto;
using aibase.RockLines.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace aibase.Models.Services.WorkflowService;

/// <summary>
///
/// </summary>
public class WorkflowService : IWorkflowService
{
    private readonly IRepository<Image, int> _imageRepository;
    private readonly IRepository<File, int> _fileRepository;
    private readonly ICropService _cropService;
    private readonly IRockLineService _rockLineService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ExternalServiceOptions _externalServiceOptions;

    /// <summary>
    /// Initializes a new instance of the PolygonOcrSegmentService class
    /// </summary>
    public WorkflowService(IHttpClientFactory httpClientFactory, IRepository<Image, int> imageRepository,
        ICropService cropService, IRockLineService rockLineService, IUnitOfWorkManager unitOfWorkManager,
        IOptions<ExternalServiceOptions> externalServiceOptions, IRepository<File, int> fileRepository)
    {
        _httpClientFactory = httpClientFactory;
        _imageRepository = imageRepository;
        _cropService = cropService;
        _rockLineService = rockLineService;
        _unitOfWorkManager = unitOfWorkManager;
        _fileRepository = fileRepository;
        _externalServiceOptions = externalServiceOptions.Value;
    }


    /// <summary>
    /// Performs OCR and segmentation analysis on an image
    /// </summary>
    /// <returns>A task representing the asynchronous operation</returns>
    /// <exception cref="ArgumentNullException">Thrown when url or image is null</exception>
    /// <exception cref="HttpRequestException">Thrown when the API request fails</exception>
    /// <exception cref="InvalidOperationException">Thrown when response deserialization fails</exception>
    public async Task<List<OcrResultV2Dto>> PerformPolygonCropOcrSegmentAsync(string url, Image image,
        ImageStatus? imageStatusDone, string? prompt, bool segmentFlag = false)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        try
        {
            var client = _httpClientFactory.CreateClient();
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "file", "image.jpg");
            form.Add(new StringContent(image.DepthFrom.ToString(CultureInfo.InvariantCulture)), "depth_from");
            form.Add(new StringContent(image.DepthTo.ToString(CultureInfo.InvariantCulture)), "depth_to");

            // Prompt
            if (!string.IsNullOrEmpty(prompt))
            {
                var modifiedPrompt = prompt
                    .Replace("DFm", image.DepthFrom.ToString(CultureInfo.InvariantCulture))
                    .Replace("DTm", image.DepthTo.ToString(CultureInfo.InvariantCulture));
                form.Add(new StringContent(modifiedPrompt), "prompt");
            }

            form.Add(new StringContent(segmentFlag.ToString().ToLower()), "segment_flag");

            var response = await client.PostAsync(_externalServiceOptions.Workflow.ProcessEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<DetectionResponse>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // Get Polygon Result
            if (resultData.Detection == null)
            {
                return [];
            }

            var polygon = resultData.Detection;
            var coordinateBoudings = polygon.Predictions
                .Select(res => new CropCoordinate
                {
                    X = res.X,
                    Y = res.Y,
                    Width = res.Width,
                    Height = res.Height,
                    Id = Guid.NewGuid().ToString(),
                    Type = res.Class
                })
                .OrderBy(x => x.Y)
                .ToArray();
            var polygonBox = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropBox).ToList();
            var polygonRow = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropRow).ToList();

            // Get OCR Result
            var ocrResults = ExtractDetections(resultData);
            //var ocrResultsWithRowIndex = AddRowIndexToOcrResult(ocrResults, polygonRow);

            // Get Segment Result
            var segmentResultRaw = new SegmentResult
            {
                MaskContourPoints = resultData.Segmentation.Select(sp => sp.Points).ToList(),
                SegmentIndices = resultData.Segmentation.Select(r => r.RowIndex).OrderBy(p => p).ToArray(),
                SegmentType = resultData.Segmentation.Select(s => s.Class).ToList(),
            };
            var segmentResults = SegmentResultHandler.ConvertSegmentResult(segmentResultRaw);
            var segmentResultWithBoundingAndIntersections =
                SegmentResultHandler.ComputeBoundingAndIntersection(segmentResults);
            image.BoundingBox = JsonConvert.SerializeObject(polygonBox);
            image.BoundingRows = JsonConvert.SerializeObject(polygonRow);
            image.OcrResult = JsonConvert.SerializeObject(ocrResults);
            image.SegmentResult = JsonConvert.SerializeObject(segmentResultWithBoundingAndIntersections);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            // Crop
            var imageCrops = await _cropService.CropImg(image.Id, coordinateBoudings, true, true);

            // Rock Line
            var autoInsertRockLineDto = new AutoInsertRockLineDto
            {
                Segments = segmentResultWithBoundingAndIntersections,
                SegmentDetails = segmentResultWithBoundingAndIntersections,
                ImageCrops = imageCrops,
                DrillHoleId = image.DrillHoleId
            };
            await _rockLineService.AutoInsertRockLineAsync(autoInsertRockLineDto);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            return ocrResults;
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <inheritdoc />
    public async Task<List<ImageCrop>> PerformPolygonCropAsync(string url, Image image, ImageStatus? imageStatusDone)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        try
        {
            var client = _httpClientFactory.CreateClient();
            // client.Timeout = TimeSpan.FromMinutes(5);
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "file", "image.jpg");

            var response = await client.PostAsync(_externalServiceOptions.Workflow.ProcessEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<DetectionResponse>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // Get Polygon Result
            var polygon = resultData.Detection;
            var coordinateBoudings = polygon.Predictions
                .Select(res => new CropCoordinate
                {
                    X = res.X,
                    Y = res.Y,
                    Width = res.Width,
                    Height = res.Height,
                    Id = Guid.NewGuid().ToString(),
                    Type = res.Class
                })
                .OrderBy(x => x.Y)
                .ToArray();
            var polygonBox = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropBox).ToList();
            var polygonRow = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropRow).OrderBy(x => x.Y)
                .ToList();

            image.BoundingBox = JsonConvert.SerializeObject(polygonBox);
            image.BoundingRows = JsonConvert.SerializeObject(polygonRow);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            // Crop
            var imageCrops = await _cropService.CropImg(image.Id, coordinateBoudings, true, true);

            // Rock Line
            var segmentResult =
                JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentResult ?? "[]")?.ToList() ?? [];
            var segmentDetailResult =
                JsonConvert.DeserializeObject<List<SegmentResultDto>>(image.SegmentDetailResult ?? "[]")?.ToList() ??
                [];
            var autoInsertRockLineDto = new AutoInsertRockLineDto
            {
                Segments = segmentResult,
                SegmentDetails = segmentDetailResult,
                ImageCrops = imageCrops,
                DrillHoleId = image.DrillHoleId
            };
            await _rockLineService.AutoInsertRockLineAsync(autoInsertRockLineDto);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            return imageCrops;
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <inheritdoc />
    public async Task<List<SegmentResultDto>> PerformPolygonCropOcrSegmentDetailCorePiecesAsync(string url, Image image,
        ImageStatus? imageStatusDone,
        string? prompt)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        try
        {
            var client = _httpClientFactory.CreateClient();
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "file", "image.jpg");
            form.Add(new StringContent(image.DepthFrom.ToString(CultureInfo.InvariantCulture)), "depth_from");
            form.Add(new StringContent(image.DepthTo.ToString(CultureInfo.InvariantCulture)), "depth_to");

            // Prompt
            if (!string.IsNullOrEmpty(prompt))
            {
                var modifiedPrompt = prompt
                    .Replace("DFm", image.DepthFrom.ToString(CultureInfo.InvariantCulture))
                    .Replace("DTm", image.DepthTo.ToString(CultureInfo.InvariantCulture));
                form.Add(new StringContent(modifiedPrompt), "prompt");
            }

            var response = await client.PostAsync(_externalServiceOptions.Workflow.ProcessEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<DetectionResponse>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // Segment Detail
            var jsonPayload = JsonConvert.SerializeObject(new
            {
                image_path = url,
                detection = resultData.Detection
            }, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });

            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

            var responseSegmentDetail =
                await client.PostAsync(_externalServiceOptions.Workflow.SegmentDetailCorePiecesEndpoint, content);
            if (!responseSegmentDetail.IsSuccessStatusCode)
            {
                throw new HttpRequestException(
                    $"API request failed with status code: {responseSegmentDetail.StatusCode}");
            }

            var resultSegment = await responseSegmentDetail.Content.ReadAsStringAsync();
            var resultSegmentData = JsonConvert.DeserializeObject<SegmentDetailOutputDto>(resultSegment);

            if (resultSegmentData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            var segmentDetailResults =
                SegmentResultHandler.ConvertSegmentDetailResult(resultSegmentData.RefinedSegmentations);
            var segmentDetailResultWithBoundingAndIntersections =
                SegmentResultHandler.ComputeBoundingAndIntersection(segmentDetailResults);

            image.SegmentDetailResult = JsonConvert.SerializeObject(segmentDetailResults);

            // Get Polygon Result
            var polygon = resultData.Detection;
            var coordinateBoudings = polygon.Predictions
                .Select(res => new CropCoordinate
                {
                    X = res.X,
                    Y = res.Y,
                    Width = res.Width,
                    Height = res.Height,
                    Id = Guid.NewGuid().ToString(),
                    Type = res.Class
                })
                .OrderBy(x => x.Y)
                .ToArray();
            var polygonBox = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropBox).ToList();
            var polygonRow = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropRow).ToList();

            // Get OCR Result
            var ocrResults = ExtractDetections(resultData);
            //var ocrResultsWithRowIndex = AddRowIndexToOcrResult(ocrResults, polygonRow);

            // Get Segment Result
            var segmentResultRaw = new SegmentResult
            {
                MaskContourPoints = resultData.Segmentation.Select(sp => sp.Points).ToList(),
                SegmentIndices = resultData.Segmentation.Select(r => r.RowIndex).OrderBy(p => p).ToArray(),
                SegmentType = resultData.Segmentation.Select(s => s.Class).ToList(),
            };
            var segmentResults = SegmentResultHandler.ConvertSegmentResult(segmentResultRaw);
            var segmentResultWithBoundingAndIntersections =
                SegmentResultHandler.ComputeBoundingAndIntersection(segmentResults);
            image.BoundingBox = JsonConvert.SerializeObject(polygonBox);
            image.BoundingRows = JsonConvert.SerializeObject(polygonRow);
            image.OcrResult = JsonConvert.SerializeObject(ocrResults);
            image.SegmentResult = JsonConvert.SerializeObject(segmentResultWithBoundingAndIntersections);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            // Crop
            var imageCrops = await _cropService.CropImg(image.Id, coordinateBoudings, true, true);

            // Rock Line
            var autoInsertRockLineDto = new AutoInsertRockLineDto
            {
                Segments = segmentResultWithBoundingAndIntersections,
                SegmentDetails = segmentDetailResultWithBoundingAndIntersections,
                ImageCrops = imageCrops,
                DrillHoleId = image.DrillHoleId
            };
            await _rockLineService.AutoInsertRockLineAsync(autoInsertRockLineDto);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            return segmentResults;
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <inheritdoc />
    public async Task<List<ImageCropDto>> PerformSegmentAutoCropAsync(string url, Image image,
        ImageStatus? imageStatusDone)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        var uri = new Uri(url);
        var filename = uri.Segments[^1];

        var files = await _fileRepository.GetAll()
            .Where(x => x.ImageId == image.Id)
            .ToListAsync();

        try
        {
            var client = _httpClientFactory.CreateClient();
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "image", filename);

            var response = await client.PostAsync(_externalServiceOptions.Workflow.SegmentCropEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<SegmentAutoCropDto>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // Get Polygon Result
            var polygon = resultData.Detection;
            var coordinateBoudings = polygon.Predictions
                .Select(res => new CropCoordinate
                {
                    X = res.X,
                    Y = res.Y,
                    Width = res.Width,
                    Height = res.Height,
                    Id = Guid.NewGuid().ToString(),
                    Type = res.Class
                })
                .OrderBy(x => x.Y)
                .ToArray();
            var polygonBox = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropBox).ToList();
            var polygonRow = coordinateBoudings.Where(c => c.Type == ImageConstSettings.ImageCropRow).ToList();


            image.BoundingBox = JsonConvert.SerializeObject(polygonBox);
            image.BoundingRows = JsonConvert.SerializeObject(polygonRow);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            foreach (var file in files)
            {
                file.Url = resultData.WarpedImageUrl;
            }

            // Crop
            var imageCrops = await _cropService.CropImg(image.Id, coordinateBoudings, true, true);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            return imageCrops.Select(x => new ImageCropDto
            {
                Id = x.Id,
                UrlCroppedImage = x.UrlCroppedImage,
                MediumSize = x.MediumSize,
                Type = x.Type,
                Coordinate = x.Coordinate,
                DepthFrom = x.DepthFrom,
                DepthTo = x.DepthTo,
                ImageId = x.ImageId,
            }).ToList();
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <inheritdoc />
    public async Task<List<OcrResultV2Dto>> PerformProcessCoreOutlineAsync(string url, Image image,
        ImageStatus? imageStatusDone)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        try
        {
            var client = _httpClientFactory.CreateClient();
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "image", "image.jpg");
            form.Add(new StringContent(image.DepthFrom.ToString(CultureInfo.InvariantCulture)), "depth_from");
            form.Add(new StringContent(image.DepthTo.ToString(CultureInfo.InvariantCulture)), "depth_to");

            var response = await client.PostAsync(_externalServiceOptions.Workflow.ProcessCoreOutlineEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<ProcessCoreOutlineResponseDto>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // Get OCR Result
            var ocrResults = ExtractDetectionProcessCoreOutlines(resultData);

            image.OcrResult = JsonConvert.SerializeObject(ocrResults);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            await _unitOfWorkManager.Current.SaveChangesAsync();

            return ocrResults;
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <inheritdoc />
    public async Task<List<OcrResultV2Dto>> PerformSegmentCorePiecesAsync(string url, Image image,
        ImageStatus? imageStatusDone)
    {
        if (string.IsNullOrEmpty(url))
            throw new ArgumentNullException(nameof(url));

        if (image == null)
            throw new ArgumentNullException(nameof(image));

        
        var uri = new Uri(url);
        var filename = uri.Segments[^1];

        
        try
        {
            var client = _httpClientFactory.CreateClient();
            var imageBytes = await client.GetByteArrayAsync(url);

            using var form = new MultipartFormDataContent();
            using var byteArrayContent = new ByteArrayContent(imageBytes);
            byteArrayContent.Headers.ContentType =
                new MediaTypeHeaderValue("application/octet-stream");
            form.Add(byteArrayContent, "image", filename);

            var response = await client.PostAsync(_externalServiceOptions.Workflow.SegmentCorePiecesEndpoint, form);

            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"API request failed with status code: {response.StatusCode}");
            }

            var result = await response.Content.ReadAsStringAsync();
            var resultData = JsonConvert.DeserializeObject<ProcessCoreOutlineResponseDto>(result);

            if (resultData == null)
            {
                throw new InvalidOperationException("Failed to deserialize API response");
            }

            // TODO Segment
            // image.SegmentResult = JsonConvert.SerializeObject(segmentResultWithBoundingAndIntersections);
            image.ImageStatus = imageStatusDone ?? image.ImageStatus;

            await _imageRepository.UpdateAsync(image);

            await _unitOfWorkManager.Current.SaveChangesAsync();

            return [];
        }
        catch (HttpRequestException)
        {
            throw; // Rethrow HTTP-specific exceptions as-is
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException("Failed to parse API response", ex);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("An unexpected error occurred while processing the image", ex);
        }
    }

    /// <summary>
    /// Extract OCR detections from the detection response
    /// </summary>
    /// <param name="input">Detection response containing Google Vision OCR results</param>
    /// <returns>List of OCR results with coordinates and confidence scores</returns>
    public static List<OcrResultV2Dto> ExtractDetections(DetectionResponse input)
    {
        var ocrResult = new List<OcrResultV2Dto>();

        var entireImagePredictions = input.GoogleVisionBlockOcr;

        if (entireImagePredictions.Count > 0)
        {
            ocrResult.AddRange(entireImagePredictions
                // .Where(x => double.TryParse(x.GeminiText, out _))
                .Select(result => new OcrResultV2Dto
                {
                    id = Guid.NewGuid().ToString(),
                    type = "wooden",
                    x = result.X,
                    y = result.Y,
                    width = result.Width,
                    height = result.Height,
                    text = result.GeminiText,
                    rowIndex = result.RowIndex,
                }));
        }

        return ocrResult;
    }

    /// <summary>
    /// Extract OCR detections from the detection response
    /// </summary>
    /// <param name="input">Detection response containing Google Vision OCR results</param>
    /// <returns>List of OCR results with coordinates and confidence scores</returns>
    public static List<OcrResultV2Dto> ExtractDetectionProcessCoreOutlines(ProcessCoreOutlineResponseDto input)
    {
        var ocrResult = new List<OcrResultV2Dto>();

        var entireImagePredictions = input.Blocks;

        if (entireImagePredictions.Count > 0)
        {
            ocrResult.AddRange(entireImagePredictions
                // .Where(x => double.TryParse(x.GeminiText, out _))
                .Select(result => new OcrResultV2Dto
                {
                    id = Guid.NewGuid().ToString(),
                    type = "wooden",
                    x = result.X,
                    y = result.Y,
                    width = result.Width,
                    height = result.Height,
                    text = result.GeminiText,
                    rowIndex = null,
                }));
        }

        return ocrResult;
    }
}